# 004 Environment Extractor Component

## Overview
**Type:** Mostly Python Component (Minimal LLM usage for ambiguous cases only)
**Purpose:** Extract environments and locations with visual descriptions for storyboard generation
**Priority:** HIGH - Part of the separated extraction architecture

## LLM Usage Documentation
**When LLM is used:** Only for ambiguous environment descriptions that cannot be determined from text analysis
**What LLM does:** Provides creative visual details for environments with minimal description
**Why LLM is needed:** Some locations may be mentioned without visual details, requiring creative interpretation
**Token usage:** Minimal - only called for unclear cases, not for every environment

## Input Specification
```python
inputs = [
    DataInput(
        name="parsed_screenplay", 
        display_name="Parsed Screenplay",
        info="Structured screenplay data from parser"
    ),
    MessageInput(
        name="llm", 
        display_name="Language Model",
        info="Connect Gemini 2.5 (used only for ambiguous cases)"
    )
]
```

## Output Specification
```json
{
  "extracted_environments": [
    {
      "environment_id": "grandmothers_cottage_interior",
      "environment_name": "Grandmother's Cottage Interior",
      "environment_type": "interior",
      "location_category": "residential",
      "visual_description": "cozy cottage interior with warm lighting, wooden beams, stone fireplace, rustic furniture",
      "extraction_method": "text_analysis",
      "confidence": 0.95,
      "text_references": [
        "INT. GRANDMOTHER'S HOUSE - DAY",
        "A cozy cottage interior with warm lighting"
      ],
      "scene_appearances": [1, 3],
      "environment_properties": ["cozy", "rustic", "warm", "intimate"],
      "lighting_conditions": "warm natural light",
      "mood_atmosphere": "comfortable and safe",
      "time_of_day": "day",
      "weather_conditions": "clear",
      "related_characters": ["little_red_riding_hood", "grandmother"],
      "story_importance": "high"
    },
    {
      "environment_id": "forest_path",
      "environment_name": "Forest Path",
      "environment_type": "exterior",
      "location_category": "natural",
      "visual_description": "winding dirt path through dense forest, tall trees, dappled sunlight",
      "extraction_method": "text_analysis",
      "confidence": 0.90,
      "text_references": [
        "EXT. FOREST PATH - DAY",
        "She walks through the forest"
      ],
      "scene_appearances": [2],
      "environment_properties": ["natural", "mysterious", "secluded"],
      "lighting_conditions": "dappled sunlight through trees",
      "mood_atmosphere": "peaceful but potentially dangerous",
      "time_of_day": "day",
      "weather_conditions": "clear",
      "related_characters": ["little_red_riding_hood"],
      "story_importance": "medium"
    }
  ],
  "extraction_summary": {
    "total_environments": 8,
    "text_extracted": 7,
    "llm_enhanced": 1,
    "environment_types": {
      "interior": 4,
      "exterior": 4
    },
    "location_categories": {
      "residential": 2,
      "natural": 3,
      "commercial": 1,
      "other": 2
    },
    "ambiguous_cases": [
      {
        "environment": "mysterious_clearing",
        "issue": "No visual description provided in text",
        "resolution": "LLM provided creative interpretation"
      }
    ],
    "processing_notes": "Environment extraction completed successfully"
  }
}
```

## Core Functionality

### 1. Text-Based Environment Extraction (Primary Method)
```python
def extract_environments_from_text(self, screenplay_data: Dict) -> List[Dict]:
    """Extract environments using Python text analysis (no LLM needed)."""
    
    environments = {}
    scenes = screenplay_data.get("scenes", [])
    
    # Environment detection patterns
    location_patterns = [
        r'(INT\.|EXT\.)\s+([^-]+)\s*-\s*(DAY|NIGHT|MORNING|EVENING)',
        r'Location:\s*([^\n]+)',
        r'\b(house|cottage|castle|palace|room|kitchen|bedroom)\b',
        r'\b(forest|woods|path|road|street|park|garden)\b',
        r'\b(shop|store|market|tavern|inn|restaurant)\b',
        r'\b(church|temple|school|library|hospital)\b'
    ]
    
    environment_types = {
        "interior": ["house", "cottage", "room", "kitchen", "bedroom", "shop", "store", "tavern", "inn", "church", "temple", "school", "library"],
        "exterior": ["forest", "woods", "path", "road", "street", "park", "garden", "market"]
    }
    
    location_categories = {
        "residential": ["house", "cottage", "room", "kitchen", "bedroom"],
        "natural": ["forest", "woods", "path", "park", "garden"],
        "commercial": ["shop", "store", "market", "tavern", "inn", "restaurant"],
        "institutional": ["church", "temple", "school", "library", "hospital"]
    }
    
    for scene in scenes:
        scene_num = scene["scene_number"]
        location = scene.get("location", "")
        time = scene.get("time", "day")
        description = scene.get("description", "")
        
        # Extract from scene location
        if location:
            env_id = self._create_environment_id(location)
            
            if env_id not in environments:
                environments[env_id] = {
                    "environment_id": env_id,
                    "environment_name": location.title(),
                    "environment_type": self._determine_environment_type(location, environment_types),
                    "location_category": self._categorize_location(location, location_categories),
                    "visual_description": self._extract_visual_from_description(description),
                    "extraction_method": "text_analysis",
                    "confidence": 0.9,
                    "text_references": [],
                    "scene_appearances": [],
                    "environment_properties": self._extract_properties(location, description),
                    "lighting_conditions": self._determine_lighting(time, description),
                    "mood_atmosphere": self._determine_mood(description),
                    "time_of_day": time.lower(),
                    "weather_conditions": self._extract_weather(description),
                    "related_characters": [],
                    "story_importance": "medium"
                }
            
            # Add scene reference
            scene_header = f"{self._get_scene_type(location)} {location} - {time.upper()}"
            environments[env_id]["text_references"].append(scene_header)
            if description:
                environments[env_id]["text_references"].append(description)
            
            if scene_num not in environments[env_id]["scene_appearances"]:
                environments[env_id]["scene_appearances"].append(scene_num)
    
    return list(environments.values())

def _create_environment_id(self, name: str) -> str:
    """Create snake_case identifier from environment name."""
    return name.lower().replace(" ", "_").replace("'", "").replace("-", "_").replace(".", "")

def _determine_environment_type(self, location: str, types: Dict) -> str:
    """Determine if environment is interior or exterior."""
    location_lower = location.lower()
    
    # Check for explicit INT/EXT markers
    if "int." in location_lower or "interior" in location_lower:
        return "interior"
    if "ext." in location_lower or "exterior" in location_lower:
        return "exterior"
    
    # Check against type categories
    for env_type, keywords in types.items():
        for keyword in keywords:
            if keyword in location_lower:
                return env_type
    
    return "unknown"

def _categorize_location(self, location: str, categories: Dict) -> str:
    """Categorize location by function."""
    location_lower = location.lower()
    
    for category, keywords in categories.items():
        for keyword in keywords:
            if keyword in location_lower:
                return category
    
    return "other"

def _extract_visual_from_description(self, description: str) -> str:
    """Extract visual elements from scene description."""
    if not description:
        return ""
    
    visual_keywords = [
        "cozy", "warm", "cold", "dark", "bright", "rustic", "modern", "ancient",
        "wooden", "stone", "brick", "metal", "glass", "ornate", "simple",
        "large", "small", "spacious", "cramped", "high", "low",
        "fireplace", "windows", "doors", "furniture", "decorations"
    ]
    
    visual_elements = []
    description_lower = description.lower()
    
    for keyword in visual_keywords:
        if keyword in description_lower:
            visual_elements.append(keyword)
    
    return ", ".join(visual_elements) if visual_elements else ""

def _determine_lighting(self, time: str, description: str) -> str:
    """Determine lighting conditions."""
    time_lower = time.lower()
    desc_lower = description.lower() if description else ""
    
    if "night" in time_lower or "evening" in time_lower:
        if "candle" in desc_lower or "fireplace" in desc_lower:
            return "warm artificial light"
        return "dim artificial light"
    elif "morning" in time_lower:
        return "soft natural light"
    elif "day" in time_lower:
        if "warm" in desc_lower:
            return "warm natural light"
        return "bright natural light"
    
    return "natural light"

def _determine_mood(self, description: str) -> str:
    """Determine mood and atmosphere."""
    if not description:
        return "neutral"
    
    desc_lower = description.lower()
    
    mood_keywords = {
        "cozy": "comfortable and safe",
        "warm": "welcoming and friendly",
        "dark": "mysterious or ominous",
        "bright": "cheerful and open",
        "rustic": "charming and traditional",
        "ancient": "mysterious and historic"
    }
    
    for keyword, mood in mood_keywords.items():
        if keyword in desc_lower:
            return mood
    
    return "neutral"
```

### 2. LLM Enhancement (Only for Ambiguous Cases)
```python
def enhance_ambiguous_environments(self, environments: List[Dict]) -> List[Dict]:
    """Use LLM only for environments with insufficient visual description."""
    
    ambiguous_environments = []
    enhanced_environments = []
    
    for env in environments:
        if len(env["visual_description"]) < 20:  # Minimal description threshold
            ambiguous_environments.append(env)
        else:
            enhanced_environments.append(env)
    
    if not ambiguous_environments:
        return environments  # No LLM needed
    
    # Use LLM only for ambiguous cases
    for env in ambiguous_environments:
        enhanced = self._enhance_environment_with_llm(env)
        enhanced_environments.append(enhanced)
    
    return enhanced_environments

def _enhance_environment_with_llm(self, environment: Dict) -> Dict:
    """Use LLM to enhance environment with minimal visual description."""
    
    prompt = f"""You are a visual description specialist for storyboard generation.

Environment: {environment['environment_name']}
Type: {environment['environment_type']} {environment['location_category']}
Time: {environment['time_of_day']}
Current description: {environment['visual_description']}
Text references: {environment['text_references']}

Provide a concise visual description for storyboard generation. Focus on:
- Architecture and structure
- Materials and textures
- Lighting and atmosphere
- Key visual elements
- Mood and feeling

Respond with JSON:
{{
  "enhanced_visual_description": "detailed visual description here",
  "enhancement_notes": "explanation of additions made"
}}"""

    try:
        response = self.llm.invoke(prompt)
        result = self._parse_llm_response(response)
        
        environment["visual_description"] = result.get("enhanced_visual_description", environment["visual_description"])
        environment["extraction_method"] = "llm_enhanced"
        environment["enhancement_notes"] = result.get("enhancement_notes", "")
        
        return environment
        
    except Exception as e:
        # Fallback: keep original description
        environment["enhancement_notes"] = f"LLM enhancement failed: {str(e)}"
        return environment
```

## Implementation Details

### Complete Component Code Structure
```python
from langflow.custom import Component
from langflow.io import DataInput, Output
from langflow.inputs import MessageInput
from langflow.schema import Data
import json
import re
from typing import Dict, List

class EnvironmentExtractor(Component):
    display_name = "004 Environment Extractor"
    description = "Extract environments and locations with visual descriptions for storyboard generation"
    
    inputs = [
        DataInput(name="parsed_screenplay", display_name="Parsed Screenplay"),
        MessageInput(name="llm", display_name="Language Model")
    ]
    
    outputs = [
        Output(display_name="Extracted Environments", name="extracted_environments", method="extract_environments")
    ]
    
    def extract_environments(self) -> Data:
        """Main extraction method with minimal LLM usage."""
        try:
            screenplay_data = self.parsed_screenplay.data
            
            if not screenplay_data or "scenes" not in screenplay_data:
                return Data(data=self._create_error_response("Invalid screenplay data"))
            
            # Step 1: Extract environments using text analysis (Python only)
            environments = self.extract_environments_from_text(screenplay_data)
            
            # Step 2: Assess story importance (Python only)
            environments = self.assess_story_importance(environments)
            
            # Step 3: Enhance only ambiguous cases with LLM (minimal usage)
            environments = self.enhance_ambiguous_environments(environments)
            
            # Step 4: Create summary
            summary = self._create_extraction_summary(environments)
            
            result = {
                "extracted_environments": environments,
                "extraction_summary": summary
            }
            
            return Data(data=result)
            
        except Exception as e:
            return Data(data=self._create_error_response(f"Environment extraction failed: {str(e)}"))
```

## Langflow Integration

### Connection Instructions
**Inputs:**
- Screenplay Parser → `parsed_screenplay`
- Language Model (Gemini) → `llm` (used minimally)

**Output:** `extracted_environments` → Description Enhancer

### Benefits of This Approach
- ✅ **Focused extraction** - Only handles environments and locations
- ✅ **Minimal LLM usage** - Python does most work
- ✅ **Industry standard** - snake_case identifiers
- ✅ **No limitations** - Processes complete movies
- ✅ **Better organization** - Separated from characters/objects
- ✅ **Atmosphere detection** - Mood and lighting analysis

## Next Steps
1. Implement in parallel with other extractors
2. Test environment extraction with your narrative
3. Verify minimal LLM usage
4. Connect to Other Elements Extractor (parallel processing)
5. Continue with separated extraction architecture
