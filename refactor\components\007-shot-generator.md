# 007 Shot Generator Component

## Overview
**Type:** <PERSON><PERSON> Component (Director of Photography)
**Purpose:** Generate cinematic shot compositions and camera directions for storyboard generation
**Priority:** HIGH - Cinematic composition phase of the workflow

## LLM Usage Documentation
**When LLM is used:** For all enhanced descriptions to create professional cinematic shot compositions
**What LLM does:** Acts as a Director of Photography, creating detailed camera angles, movements, and cinematic compositions
**Why LLM is needed:** Professional cinematography requires creative expertise in visual storytelling, camera work, and composition
**Token usage:** Moderate - processes enhanced descriptions with focused cinematography prompts

## Input Specification
```python
inputs = [
    DataInput(
        name="enhanced_descriptions", 
        display_name="Enhanced Descriptions",
        info="Enhanced visual descriptions from description enhancer"
    ),
    MessageInput(
        name="llm", 
        display_name="Language Model",
        info="Connect Gemini 2.5 for Director of Photography expertise"
    )
]
```

## Output Specification
```json
{
  "generated_shots": [
    {
      "shot_id": "shot_001_little_red_introduction",
      "scene_number": 1,
      "shot_number": 1,
      "shot_type": "medium_shot",
      "camera_angle": "eye_level",
      "camera_movement": "static",
      "composition": "rule_of_thirds",
      "focal_elements": ["little_red_riding_hood", "wicker_basket"],
      "background_elements": ["cottage_door", "garden_path"],
      "lighting_direction": "natural_front_lighting",
      "depth_of_field": "medium",
      "visual_style": "warm_inviting",
      "shot_description": "Medium shot of Little Red Riding Hood standing at her cottage door, positioned on the right third of the frame. She holds her wicker basket prominently, with the garden path leading into the forest visible in the background. Natural morning light illuminates her face, creating a warm, inviting atmosphere that establishes her innocent character before the journey begins.",
      "camera_notes": "Static camera at eye level to establish character connection. Frame her slightly off-center to show the path ahead, suggesting the journey to come.",
      "lighting_notes": "Soft morning light from camera left, creating gentle shadows that add dimension without harshness. Avoid dramatic lighting to maintain innocent tone.",
      "composition_notes": "Use rule of thirds with character on right third, path on left third. Leave headroom to show cottage architecture. Basket should be clearly visible as important story element.",
      "transition_to_next": "cut_to_wide_shot"
    },
    {
      "shot_id": "shot_002_forest_path_establishing",
      "scene_number": 1,
      "shot_number": 2,
      "shot_type": "wide_shot",
      "camera_angle": "slightly_high",
      "camera_movement": "slow_push_in",
      "composition": "leading_lines",
      "focal_elements": ["forest_path", "little_red_riding_hood"],
      "background_elements": ["towering_trees", "dappled_sunlight"],
      "lighting_direction": "overhead_filtered",
      "depth_of_field": "deep",
      "visual_style": "mysterious_beautiful",
      "shot_description": "Wide establishing shot of the forest path with Little Red Riding Hood as a small figure beginning her journey. The winding path creates leading lines that draw the eye deeper into the mysterious forest. Towering trees frame the shot naturally, while dappled sunlight filters through the canopy, creating a beautiful but slightly ominous atmosphere.",
      "camera_notes": "Start wide and slowly push in to create sense of entering the forest world. Slightly high angle to show path layout and forest scale.",
      "lighting_notes": "Filtered sunlight through leaves creates natural spotlighting effects. Use contrast between bright path and darker forest depths.",
      "composition_notes": "Path as strong leading line from foreground to background. Frame trees to create natural borders. Character small but visible to show scale.",
      "transition_to_next": "match_cut_to_medium"
    }
  ],
  "cinematography_summary": {
    "total_shots": 24,
    "shot_types": {
      "wide_shot": 6,
      "medium_shot": 8,
      "close_up": 5,
      "extreme_close_up": 2,
      "establishing_shot": 3
    },
    "camera_movements": {
      "static": 12,
      "slow_push_in": 4,
      "slow_pull_out": 3,
      "pan_left": 2,
      "pan_right": 2,
      "tilt_up": 1
    },
    "visual_themes": [
      "innocence_to_danger_progression",
      "natural_beauty_with_hidden_threats",
      "intimate_character_moments",
      "environmental_storytelling"
    ],
    "cinematography_style": "classical_narrative_with_modern_sensibility",
    "technical_notes": "Consistent eye-level perspective for character connection, strategic use of camera movement for emotional impact"
  }
}
```

## Core Functionality

### 1. Shot Planning and Scene Analysis
```python
def analyze_scenes_for_shots(self, enhanced_descriptions: Dict) -> List[Dict]:
    """Analyze enhanced descriptions to plan shot sequences."""
    
    shot_plans = []
    
    # Extract scene information from enhanced descriptions
    scenes = self._extract_scene_structure(enhanced_descriptions)
    
    for scene_num, scene_data in enumerate(scenes, 1):
        # Plan shots for this scene
        scene_shots = self._plan_scene_shots(scene_num, scene_data)
        shot_plans.extend(scene_shots)
    
    return shot_plans

def _plan_scene_shots(self, scene_num: int, scene_data: Dict) -> List[Dict]:
    """Plan shot sequence for a single scene."""
    
    shots = []
    
    # Determine shot types based on scene content
    if scene_data.get("is_establishing", False):
        shots.append(self._create_shot_plan("establishing_shot", scene_num, len(shots) + 1, scene_data))
    
    # Add character introduction shots
    for character in scene_data.get("characters", []):
        shots.append(self._create_shot_plan("medium_shot", scene_num, len(shots) + 1, scene_data, character))
    
    # Add environment shots
    if scene_data.get("environments"):
        shots.append(self._create_shot_plan("wide_shot", scene_num, len(shots) + 1, scene_data))
    
    # Add detail shots for important objects
    for obj in scene_data.get("important_objects", []):
        shots.append(self._create_shot_plan("close_up", scene_num, len(shots) + 1, scene_data, obj))
    
    return shots

def _create_shot_plan(self, shot_type: str, scene_num: int, shot_num: int, scene_data: Dict, focus_element: Dict = None) -> Dict:
    """Create a basic shot plan structure."""
    
    return {
        "scene_number": scene_num,
        "shot_number": shot_num,
        "planned_shot_type": shot_type,
        "focus_element": focus_element,
        "scene_data": scene_data,
        "needs_cinematography": True
    }
```

### 2. Director of Photography LLM Processing
```python
def generate_cinematography(self, shot_plans: List[Dict]) -> List[Dict]:
    """Generate professional cinematography for each planned shot using LLM."""
    
    generated_shots = []
    
    # Process shots in batches for efficiency
    batch_size = 3
    for i in range(0, len(shot_plans), batch_size):
        batch = shot_plans[i:i + batch_size]
        batch_shots = self._generate_shot_batch(batch)
        generated_shots.extend(batch_shots)
    
    return generated_shots

def _generate_shot_batch(self, shot_batch: List[Dict]) -> List[Dict]:
    """Generate cinematography for a batch of shots using Director of Photography expertise."""
    
    prompt = self._create_cinematography_prompt(shot_batch)
    
    try:
        response = self.llm.invoke(prompt)
        shots = self._parse_cinematography_response(response, shot_batch)
        return shots
        
    except Exception as e:
        # Fallback: create basic shots
        return self._create_fallback_shots(shot_batch)

def _create_cinematography_prompt(self, shot_batch: List[Dict]) -> str:
    """Create Director of Photography prompt for shot generation."""
    
    shot_descriptions = []
    for i, shot in enumerate(shot_batch):
        scene_data = shot["scene_data"]
        focus = shot.get("focus_element", {})
        
        desc = f"""Shot {i+1}:
- Scene: {shot['scene_number']}
- Planned type: {shot['planned_shot_type']}
- Focus element: {focus.get('name', 'scene general')}
- Characters present: {[c.get('character_name', 'Unknown') for c in scene_data.get('characters', [])]}
- Environment: {scene_data.get('environment_name', 'Unknown')}
- Key objects: {[o.get('object_name', 'Unknown') for o in scene_data.get('objects', [])]}
- Mood elements: {[e.get('element_name', 'Unknown') for e in scene_data.get('other_elements', [])]}"""
        
        shot_descriptions.append(desc)
    
    prompt = f"""You are a professional Director of Photography working on a storyboard project. 

Create detailed cinematography for these shots:

{chr(10).join(shot_descriptions)}

For each shot, provide professional cinematography decisions including:

1. **Shot Type**: wide_shot, medium_shot, close_up, extreme_close_up, establishing_shot
2. **Camera Angle**: eye_level, high_angle, low_angle, bird_eye, worm_eye
3. **Camera Movement**: static, slow_push_in, slow_pull_out, pan_left, pan_right, tilt_up, tilt_down, dolly_left, dolly_right
4. **Composition**: rule_of_thirds, center_composition, leading_lines, symmetrical, asymmetrical
5. **Lighting Direction**: natural_front_lighting, side_lighting, back_lighting, overhead_filtered, dramatic_side, soft_diffused
6. **Depth of Field**: shallow, medium, deep
7. **Visual Style**: warm_inviting, mysterious_beautiful, dramatic_tense, soft_romantic, stark_realistic

Focus on:
- Professional cinematography principles
- Visual storytelling that supports narrative
- Smooth transitions between shots
- Consistent visual style
- Clear technical direction for storyboard artists

Respond with JSON:
{{
  "shots": [
    {{
      "shot_index": 0,
      "shot_id": "descriptive_shot_id",
      "shot_type": "medium_shot",
      "camera_angle": "eye_level",
      "camera_movement": "static",
      "composition": "rule_of_thirds",
      "focal_elements": ["element1", "element2"],
      "background_elements": ["bg1", "bg2"],
      "lighting_direction": "natural_front_lighting",
      "depth_of_field": "medium",
      "visual_style": "warm_inviting",
      "shot_description": "Detailed description of the shot composition and visual elements",
      "camera_notes": "Technical camera direction for operators",
      "lighting_notes": "Specific lighting setup and mood direction",
      "composition_notes": "Framing and composition guidance",
      "transition_to_next": "cut_to_wide_shot"
    }}
  ]
}}"""

    return prompt
```

## Implementation Details

### Complete Component Code Structure
```python
from langflow.custom import Component
from langflow.io import DataInput, Output
from langflow.inputs import MessageInput
from langflow.schema import Data
import json
from typing import Dict, List

class ShotGenerator(Component):
    display_name = "007 Shot Generator"
    description = "Generate cinematic shot compositions using Director of Photography expertise"
    
    inputs = [
        DataInput(name="enhanced_descriptions", display_name="Enhanced Descriptions"),
        MessageInput(name="llm", display_name="Language Model")
    ]
    
    outputs = [
        Output(display_name="Generated Shots", name="generated_shots", method="generate_shots")
    ]
    
    def generate_shots(self) -> Data:
        """Main shot generation method using Director of Photography LLM expertise."""
        try:
            enhanced_data = self.enhanced_descriptions.data
            
            if not enhanced_data or "enhanced_descriptions" not in enhanced_data:
                return Data(data=self._create_error_response("No enhanced descriptions provided"))
            
            # Step 1: Analyze scenes and plan shots
            shot_plans = self.analyze_scenes_for_shots(enhanced_data["enhanced_descriptions"])
            
            if not shot_plans:
                return Data(data=self._create_error_response("No shots could be planned"))
            
            # Step 2: Generate professional cinematography
            generated_shots = self.generate_cinematography(shot_plans)
            
            # Step 3: Create cinematography summary
            summary = self._create_cinematography_summary(generated_shots)
            
            result = {
                "generated_shots": generated_shots,
                "cinematography_summary": summary
            }
            
            return Data(data=result)
            
        except Exception as e:
            return Data(data=self._create_error_response(f"Shot generation failed: {str(e)}"))
```

## Langflow Integration

### Connection Instructions
**Inputs:**
- Description Enhancer → `enhanced_descriptions`
- Language Model (Gemini) → `llm`

**Output:** `generated_shots` → Scene Descriptor

### Benefits of This Approach
- ✅ **Professional cinematography** - Expert Director of Photography guidance
- ✅ **Complete shot planning** - Comprehensive scene coverage
- ✅ **Technical precision** - Detailed camera and lighting direction
- ✅ **Visual storytelling** - Shots support narrative flow
- ✅ **Artist-ready output** - Clear technical guidance for storyboard creation
- ✅ **No limitations** - Processes complete movies

## Next Steps
1. Connect enhanced descriptions to this component
2. Test cinematography generation with your narrative
3. Verify professional shot composition and technical direction
4. Connect to Scene Descriptor for data combination
5. Continue with scene assembly phase
