# Langflow Connection Instructions - Separated Extraction Architecture

## Overview
This guide provides step-by-step instructions for connecting the 10 specialized components in your Langflow workflow for professional storyboard generation.

## Architecture Overview

### ✅ New Separated Extraction Architecture
```
File → 001-Screenplay-Parser →
├── 002-Character-Extractor → Language Model (Gemini)
├── 003-Object-Extractor → Language Model (Gemini)
├── 004-Environment-Extractor → Language Model (Gemini)
└── 005-Other-Elements-Extractor → Language Model (Gemini)
                ↓
006-Description-Enhancer → Language Model (Gemini)
                ↓
007-Shot-Generator → Language Model (Gemini)
                ↓
008-Scene-Descriptor (Pure Python)
                ↓
009-Consistency-Manager (Pure Python)
                ↓
010-Storyboard-Optimizer → Language Model (Gemini)
```

**Benefits:**
- **Separated extraction** - Each element type handled by specialized component
- **Minimal LLM usage** - Python handles 80%+ of extraction work
- **User-defined character limits** - Complete control over prompt length
- **Professional output** - Ready for video generation platforms
- **No quality settings** - All components use best quality
- **Complete movie processing** - No artificial limitations

## Step-by-Step Connection Guide

### Step 1: Foundation Components (Pure Python)

#### Component 1: Screenplay Parser
**Component Type:** Python Code
**Name:** "001 Screenplay Parser"
**Code:** Copy from `components/001-screenplay-parser.md`

**Connections:**
- **Input:** File component → `narrative_text`
- **Output:** `parsed_screenplay` → All 4 extractors

#### Component 8: Scene Descriptor
**Component Type:** Python Code
**Name:** "008 Scene Descriptor"
**Code:** Copy from `components/008-scene-descriptor.md`

**Connections:**
- **Inputs:**
  - Screenplay Parser → `parsed_screenplay`
  - Description Enhancer → `enhanced_descriptions`
  - Shot Generator → `generated_shots`
- **Output:** `complete_scenes` → Consistency Manager

#### Component 9: Consistency Manager
**Component Type:** Python Code
**Name:** "009 Consistency Manager"
**Code:** Copy from `components/009-consistency-manager.md`

**Connections:**
- **Input:** Scene Descriptor → `complete_scenes`
- **Output:** `consistency_report` → Storyboard Optimizer

### Step 2: Extraction Components (Minimal LLM)

#### Component 2: Character Extractor
**Component Type:** Python Code
**Name:** "002 Character Extractor"
**Code:** Copy from `components/002-character-extractor.md`

**Connections:**
- **Inputs:**
  - Screenplay Parser → `parsed_screenplay`
  - Language Model (Gemini 2.5) → `llm`
- **Output:** `extracted_characters` → Description Enhancer

#### Component 3: Object Extractor
**Component Type:** Python Code
**Name:** "003 Object Extractor"
**Code:** Copy from `components/003-object-extractor.md`

**Connections:**
- **Inputs:**
  - Screenplay Parser → `parsed_screenplay`
  - Language Model (Gemini 2.5) → `llm`
- **Output:** `extracted_objects` → Description Enhancer

#### Component 4: Environment Extractor
**Component Type:** Python Code
**Name:** "004 Environment Extractor"
**Code:** Copy from `components/004-environment-extractor.md`

**Connections:**
- **Inputs:**
  - Screenplay Parser → `parsed_screenplay`
  - Language Model (Gemini 2.5) → `llm`
- **Output:** `extracted_environments` → Description Enhancer

#### Component 5: Other Elements Extractor
**Component Type:** Python Code
**Name:** "005 Other Elements Extractor"
**Code:** Copy from `components/005-other-elements-extractor.md`

**Connections:**
- **Inputs:**
  - Screenplay Parser → `parsed_screenplay`
  - Language Model (Gemini 2.5) → `llm`
- **Output:** `extracted_other_elements` → Description Enhancer

### Step 3: Creative Enhancement Components (LLM)

#### Component 6: Description Enhancer
**Component Type:** Python Code
**Name:** "006 Description Enhancer"
**Code:** Copy from `components/006-description-enhancer.md`

**Connections:**
- **Inputs:**
  - Character Extractor → `extracted_characters`
  - Object Extractor → `extracted_objects`
  - Environment Extractor → `extracted_environments`
  - Other Elements Extractor → `extracted_other_elements`
  - Language Model (Gemini 2.5) → `llm`
- **Output:** `enhanced_descriptions` → Shot Generator

#### Component 7: Shot Generator
**Component Type:** Python Code
**Name:** "007 Shot Generator"
**Code:** Copy from `components/007-shot-generator.md`

**Connections:**
- **Inputs:**
  - Description Enhancer → `enhanced_descriptions`
  - Language Model (Gemini 2.5) → `llm`
- **Output:** `generated_shots` → Scene Descriptor

### Step 4: Final Optimization Component (LLM)

#### Component 10: Storyboard Optimizer
**Component Type:** Python Code
**Name:** "010 Storyboard Optimizer"
**Code:** Copy from `components/010-storyboard-optimizer.md`

**Connections:**
- **Inputs:**
  - Consistency Manager → `consistency_report`
  - User Input → `max_prompt_length` (IntInput, default: 500)
  - User Input → `min_prompt_length` (IntInput, default: 200)
  - User Input → `optimization_focus` (DropdownInput: visual_detail, narrative_flow, technical_precision, artistic_style, balanced)
  - Language Model (Gemini 2.5) → `llm`
- **Output:** `optimized_storyboard` → Final Output

### Step 5: Configure Language Model
**Component Type:** Language Model (Gemini)
**Settings:**
- **Model:** gemini-2.5-flash (or your preferred Gemini model)
- **Temperature:** 0.7
- **Max Tokens:** 2048 (conservative to avoid cutoffs)
- **System Message:** Will be set by each Python component

**Connections:**
This single Language Model component connects to:
- Character Extractor
- Object Extractor
- Environment Extractor
- Other Elements Extractor
- Description Enhancer
- Shot Generator
- Storyboard Optimizer

### Step 6: Configure User Input Components
For user-configurable settings, add these input components:

#### Max Prompt Length
**Component Type:** IntInput
**Name:** "Max Prompt Length"
**Default Value:** 500
**Description:** Maximum character count for each storyboard prompt

#### Min Prompt Length
**Component Type:** IntInput
**Name:** "Min Prompt Length"
**Default Value:** 200
**Description:** Minimum character count for each storyboard prompt

#### Optimization Focus
**Component Type:** DropdownInput
**Name:** "Optimization Focus"
**Options:** ["visual_detail", "narrative_flow", "technical_precision", "artistic_style", "balanced"]
**Default Value:** "balanced"
**Description:** Focus area for prompt optimization

### Step 7: Complete Connection Flow

**Final Workflow Connections:**
```
File → 001-Screenplay-Parser →
├── 002-Character-Extractor → Gemini LLM
├── 003-Object-Extractor → Gemini LLM
├── 004-Environment-Extractor → Gemini LLM
└── 005-Other-Elements-Extractor → Gemini LLM
                ↓
006-Description-Enhancer → Gemini LLM
                ↓
007-Shot-Generator → Gemini LLM
                ↓
008-Scene-Descriptor (Pure Python)
                ↓
009-Consistency-Manager (Pure Python)
                ↓
010-Storyboard-Optimizer → Gemini LLM
                ↓
Final Optimized Storyboard Output
```

## Component Configuration Details

### Python Component Setup
For each Python component:

1. **Create new Python Code component**
2. **Copy the component code** from the respective `.md` file
3. **Set component name** (e.g., "001 Screenplay Parser")
4. **Configure inputs** according to the specifications
5. **Test component individually** before connecting

### Language Model Configuration
**Important Settings for Gemini 2.5:**
```json
{
  "model": "gemini-2.5-flash",
  "temperature": 0.7,
  "max_tokens": 2048,
  "top_p": 0.9,
  "frequency_penalty": 0,
  "presence_penalty": 0
}
```

**Why these settings:**
- **max_tokens: 2048** - Conservative limit to prevent cutoffs
- **temperature: 0.7** - Balance between creativity and consistency
- **top_p: 0.9** - Good balance while avoiding repetition

## Testing Strategy

### Phase 1: Foundation Testing (Pure Python)
Test foundation components first:

1. **001-Screenplay Parser**
   - Input: Your narrative text
   - Expected: Structured JSON with scenes and characters
   - Validation: Check all required fields present

2. **008-Scene Descriptor**
   - Input: Mock data from all extractors
   - Expected: Complete scene descriptions
   - Validation: All elements properly integrated

3. **009-Consistency Manager**
   - Input: Complete scenes
   - Expected: Consistency validation report
   - Validation: Issues detected and reported

### Phase 2: Extraction Testing (Minimal LLM)
Test extraction components:

1. **002-Character Extractor**
   - Input: Parsed screenplay + LLM
   - Expected: Character data with minimal LLM usage
   - Validation: 80%+ extracted via Python, LLM only for ambiguous cases

2. **003-Object Extractor**
   - Input: Parsed screenplay + LLM
   - Expected: Object data with minimal LLM usage
   - Validation: Efficient extraction with fallbacks

3. **004-Environment Extractor**
   - Input: Parsed screenplay + LLM
   - Expected: Environment data with minimal LLM usage
   - Validation: Comprehensive location extraction

4. **005-Other Elements Extractor**
   - Input: Parsed screenplay + LLM
   - Expected: Other elements (emotions, effects, atmosphere)
   - Validation: Creative elements properly identified

### Phase 3: Enhancement Testing (LLM)
Test creative enhancement components:

1. **006-Description Enhancer**
   - Input: All extracted elements + LLM
   - Expected: Rich, creative visual descriptions
   - Validation: Artistic enhancement quality

2. **007-Shot Generator**
   - Input: Enhanced descriptions + LLM
   - Expected: Professional cinematography
   - Validation: Director of Photography quality

### Phase 4: Optimization Testing (LLM)
Test final optimization:

1. **010-Storyboard Optimizer**
   - Input: Consistency report + user settings + LLM
   - Expected: Optimized prompts within character limits
   - Validation: User-defined constraints met

### Phase 5: Full Workflow Testing
Test complete workflow with your data:
- Monitor token usage at each LLM call
- Verify user-defined character limits are respected
- Check final storyboard quality for video generation

## Troubleshooting

### Issue: Character Limit Compliance
**Symptoms:** Final prompts exceed or fall short of user-defined limits
**Solution:**
- Check 010-Storyboard-Optimizer configuration
- Verify user input components are connected
- Test intelligent truncation/expansion logic

### Issue: Component Connection Errors
**Symptoms:** Data not flowing between components
**Solution:**
- Check input/output names match exactly
- Verify data types are compatible
- Test components individually first

### Issue: LLM Token Usage
**Symptoms:** High token consumption or API limits
**Solution:**
- Verify minimal LLM usage in extraction components
- Check batching efficiency in enhancement components
- Monitor token usage per component

### Issue: Consistency Validation Failures
**Symptoms:** 009-Consistency-Manager reports many issues
**Solution:**
- Review extraction component outputs
- Check element naming consistency
- Verify visual description alignment

## Performance Monitoring

### Key Metrics to Track
1. **User Setting Compliance**
   - Target: 100% prompts within user-defined character limits
   - Monitor: Character count distribution

2. **LLM Usage Efficiency**
   - Target: Minimal usage in extraction, strategic usage in enhancement
   - Monitor: Token usage per component type

3. **Processing Quality**
   - Target: Professional storyboard output ready for video generation
   - Monitor: Consistency scores and validation results

4. **Component Performance**
   - Target: Each component completes successfully
   - Monitor: Error rates and processing times

### Success Criteria
- ✅ **All 10 components working** with proper specifications
- ✅ **User-defined character limits** respected in final output
- ✅ **No quality settings** - all components use best quality
- ✅ **Complete movie processing** without limitations
- ✅ **Professional storyboard output** ready for video generation
- ✅ **Efficient LLM usage** with minimal token consumption
- ✅ **Industry standard identifiers** (snake_case throughout)

## Next Steps
1. **Implement components in order** (Foundation → Extraction → Enhancement → Optimization)
2. **Test each phase** before proceeding to next
3. **Configure user settings** for character limits and optimization focus
4. **Validate final output** meets video generation requirements
5. **Document any customizations** needed for your specific use case
