# 008 Scene Descriptor Component

## Overview
**Type:** Pure Python Component (Data Combination)
**Purpose:** Combine all previous component outputs into complete scene descriptions for storyboard generation
**Priority:** HIGH - Data assembly phase of the workflow

## LLM Usage Documentation
**When LLM is used:** Never - This component uses only Python data processing
**What LLM does:** N/A - Pure Python component
**Why LLM is not needed:** Data combination and structuring can be handled efficiently with Python logic
**Token usage:** None - No LLM calls

## Input Specification
```python
inputs = [
    DataInput(
        name="parsed_screenplay", 
        display_name="Parsed Screenplay",
        info="Original screenplay structure from parser"
    ),
    DataInput(
        name="enhanced_descriptions", 
        display_name="Enhanced Descriptions",
        info="Enhanced visual descriptions from description enhancer"
    ),
    DataInput(
        name="generated_shots", 
        display_name="Generated Shots",
        info="Cinematography data from shot generator"
    )
]
```

## Output Specification
```json
{
  "complete_scenes": [
    {
      "scene_id": "scene_001",
      "scene_number": 1,
      "scene_title": "Little <PERSON> Hood Begins Her Journey",
      "location": "Cottage and Forest Path",
      "time": "Morning",
      "narrative_description": "Little Red <PERSON> prepares to visit her grandmother, beginning her journey through the forest.",
      "shots": [
        {
          "shot_id": "shot_001_little_red_introduction",
          "shot_number": 1,
          "shot_type": "medium_shot",
          "camera_angle": "eye_level",
          "camera_movement": "static",
          "composition": "rule_of_thirds",
          "shot_description": "Medium shot of Little Red Riding Hood standing at her cottage door...",
          "elements": {
            "characters": [
              {
                "character_id": "little_red_riding_hood",
                "character_name": "Little Red Riding Hood",
                "enhanced_description": "A spirited young girl with flowing auburn hair...",
                "position_in_shot": "right_third",
                "visual_prominence": "primary_focus"
              }
            ],
            "objects": [
              {
                "object_id": "wicker_basket",
                "object_name": "Wicker Basket",
                "enhanced_description": "An intricately woven wicker basket...",
                "position_in_shot": "character_hands",
                "visual_prominence": "secondary_focus"
              }
            ],
            "environments": [
              {
                "environment_id": "cottage_exterior",
                "environment_name": "Cottage Exterior",
                "enhanced_description": "A charming countryside cottage...",
                "position_in_shot": "background",
                "visual_prominence": "context"
              }
            ],
            "other_elements": [
              {
                "element_id": "morning_atmosphere",
                "element_name": "Morning Atmosphere",
                "enhanced_description": "Soft morning light creates a warm, inviting mood...",
                "position_in_shot": "overall_lighting",
                "visual_prominence": "atmospheric"
              }
            ]
          },
          "technical_specs": {
            "lighting_direction": "natural_front_lighting",
            "depth_of_field": "medium",
            "visual_style": "warm_inviting",
            "camera_notes": "Static camera at eye level to establish character connection",
            "lighting_notes": "Soft morning light from camera left, creating gentle shadows",
            "composition_notes": "Use rule of thirds with character on right third, path on left third"
          },
          "storyboard_instructions": {
            "primary_focus": "Character introduction and emotional connection",
            "key_visual_elements": ["character_expression", "basket_prominence", "cottage_context"],
            "artistic_emphasis": "Innocence and warmth before the journey",
            "technical_requirements": ["clear_character_visibility", "basket_detail", "path_suggestion"]
          }
        }
      ],
      "scene_summary": {
        "total_shots": 4,
        "primary_characters": ["Little Red Riding Hood"],
        "key_objects": ["Wicker Basket", "Cottage Door"],
        "main_environment": "Cottage Exterior to Forest Path",
        "dominant_mood": "Warm and Innocent",
        "narrative_purpose": "Character introduction and journey beginning",
        "visual_themes": ["innocence", "preparation", "journey_start"],
        "cinematography_style": "Classical narrative introduction"
      },
      "continuity_notes": {
        "character_consistency": ["Little Red Riding Hood appearance", "Basket presence"],
        "environmental_consistency": ["Cottage architecture", "Path direction"],
        "lighting_consistency": ["Morning light quality", "Warm color temperature"],
        "prop_tracking": ["Basket throughout scene", "Cottage door state"]
      }
    }
  ],
  "assembly_summary": {
    "total_scenes": 8,
    "total_shots": 24,
    "data_integration": {
      "screenplay_scenes_processed": 8,
      "enhanced_elements_integrated": 45,
      "shots_with_complete_data": 24,
      "missing_data_instances": 0
    },
    "consistency_tracking": {
      "character_appearances_mapped": 12,
      "object_continuity_tracked": 18,
      "environment_transitions_noted": 6,
      "lighting_consistency_maintained": true
    },
    "storyboard_readiness": "Complete - All scenes have full visual and technical specifications"
  }
}
```

## Core Functionality

### 1. Data Collection and Validation
```python
def collect_and_validate_inputs(self) -> Dict:
    """Collect and validate all input data for scene assembly."""
    
    inputs = {
        "screenplay": None,
        "enhanced_descriptions": None,
        "generated_shots": None
    }
    
    # Collect screenplay data
    if self.parsed_screenplay and hasattr(self.parsed_screenplay, 'data'):
        inputs["screenplay"] = self.parsed_screenplay.data
    
    # Collect enhanced descriptions
    if self.enhanced_descriptions and hasattr(self.enhanced_descriptions, 'data'):
        enhanced_data = self.enhanced_descriptions.data
        if "enhanced_descriptions" in enhanced_data:
            inputs["enhanced_descriptions"] = enhanced_data["enhanced_descriptions"]
    
    # Collect generated shots
    if self.generated_shots and hasattr(self.generated_shots, 'data'):
        shots_data = self.generated_shots.data
        if "generated_shots" in shots_data:
            inputs["generated_shots"] = shots_data["generated_shots"]
    
    # Validate required data
    missing_data = [key for key, value in inputs.items() if value is None]
    if missing_data:
        raise ValueError(f"Missing required data: {missing_data}")
    
    return inputs

def validate_data_consistency(self, inputs: Dict) -> Dict:
    """Validate consistency between different data sources."""
    
    validation_results = {
        "scene_count_match": False,
        "character_consistency": True,
        "object_consistency": True,
        "issues": []
    }
    
    screenplay_scenes = len(inputs["screenplay"].get("scenes", []))
    shot_scenes = len(set(shot["scene_number"] for shot in inputs["generated_shots"]))
    
    if screenplay_scenes == shot_scenes:
        validation_results["scene_count_match"] = True
    else:
        validation_results["issues"].append(f"Scene count mismatch: screenplay={screenplay_scenes}, shots={shot_scenes}")
    
    return validation_results
```

### 2. Scene Assembly and Data Integration
```python
def assemble_complete_scenes(self, inputs: Dict) -> List[Dict]:
    """Assemble complete scene descriptions from all input data."""
    
    complete_scenes = []
    screenplay_scenes = inputs["screenplay"].get("scenes", [])
    enhanced_descriptions = inputs["enhanced_descriptions"]
    generated_shots = inputs["generated_shots"]
    
    # Group shots by scene
    shots_by_scene = self._group_shots_by_scene(generated_shots)
    
    for scene in screenplay_scenes:
        scene_number = scene["scene_number"]
        scene_shots = shots_by_scene.get(scene_number, [])
        
        # Assemble complete scene
        complete_scene = self._assemble_single_scene(scene, scene_shots, enhanced_descriptions)
        complete_scenes.append(complete_scene)
    
    return complete_scenes

def _assemble_single_scene(self, screenplay_scene: Dict, scene_shots: List[Dict], enhanced_descriptions: Dict) -> Dict:
    """Assemble a single complete scene with all data integrated."""
    
    scene_number = screenplay_scene["scene_number"]
    
    # Create base scene structure
    complete_scene = {
        "scene_id": f"scene_{scene_number:03d}",
        "scene_number": scene_number,
        "scene_title": self._generate_scene_title(screenplay_scene),
        "location": screenplay_scene.get("location", "Unknown"),
        "time": screenplay_scene.get("time", "Unknown"),
        "narrative_description": screenplay_scene.get("description", ""),
        "shots": [],
        "scene_summary": {},
        "continuity_notes": {}
    }
    
    # Process each shot in the scene
    for shot in scene_shots:
        complete_shot = self._assemble_shot_with_elements(shot, enhanced_descriptions)
        complete_scene["shots"].append(complete_shot)
    
    # Generate scene summary
    complete_scene["scene_summary"] = self._generate_scene_summary(complete_scene)
    
    # Generate continuity notes
    complete_scene["continuity_notes"] = self._generate_continuity_notes(complete_scene)
    
    return complete_scene

def _assemble_shot_with_elements(self, shot: Dict, enhanced_descriptions: Dict) -> Dict:
    """Assemble a shot with all visual elements integrated."""
    
    # Start with shot structure
    complete_shot = {
        "shot_id": shot.get("shot_id", "unknown_shot"),
        "shot_number": shot.get("shot_number", 1),
        "shot_type": shot.get("shot_type", "medium_shot"),
        "camera_angle": shot.get("camera_angle", "eye_level"),
        "camera_movement": shot.get("camera_movement", "static"),
        "composition": shot.get("composition", "rule_of_thirds"),
        "shot_description": shot.get("shot_description", ""),
        "elements": {
            "characters": [],
            "objects": [],
            "environments": [],
            "other_elements": []
        },
        "technical_specs": {},
        "storyboard_instructions": {}
    }
    
    # Integrate enhanced elements
    focal_elements = shot.get("focal_elements", [])
    background_elements = shot.get("background_elements", [])
    
    # Find and integrate characters
    for char_data in enhanced_descriptions.get("characters", []):
        char_id = char_data.get("character_id", "")
        if char_id in focal_elements or char_id in background_elements:
            position = "primary_focus" if char_id in focal_elements else "background"
            char_element = self._create_element_entry(char_data, position)
            complete_shot["elements"]["characters"].append(char_element)
    
    # Find and integrate objects
    for obj_data in enhanced_descriptions.get("objects", []):
        obj_id = obj_data.get("object_id", "")
        if obj_id in focal_elements or obj_id in background_elements:
            position = "secondary_focus" if obj_id in focal_elements else "background"
            obj_element = self._create_element_entry(obj_data, position)
            complete_shot["elements"]["objects"].append(obj_element)
    
    # Find and integrate environments
    for env_data in enhanced_descriptions.get("environments", []):
        env_id = env_data.get("environment_id", "")
        if env_id in focal_elements or env_id in background_elements:
            position = "context" if env_id in background_elements else "primary_setting"
            env_element = self._create_element_entry(env_data, position)
            complete_shot["elements"]["environments"].append(env_element)
    
    # Find and integrate other elements
    for other_data in enhanced_descriptions.get("other_elements", []):
        other_id = other_data.get("element_id", "")
        if other_id in focal_elements or other_id in background_elements:
            position = "atmospheric"
            other_element = self._create_element_entry(other_data, position)
            complete_shot["elements"]["other_elements"].append(other_element)
    
    # Add technical specifications
    complete_shot["technical_specs"] = {
        "lighting_direction": shot.get("lighting_direction", "natural"),
        "depth_of_field": shot.get("depth_of_field", "medium"),
        "visual_style": shot.get("visual_style", "realistic"),
        "camera_notes": shot.get("camera_notes", ""),
        "lighting_notes": shot.get("lighting_notes", ""),
        "composition_notes": shot.get("composition_notes", "")
    }
    
    # Add storyboard instructions
    complete_shot["storyboard_instructions"] = self._generate_storyboard_instructions(complete_shot)
    
    return complete_shot

def _create_element_entry(self, element_data: Dict, position: str) -> Dict:
    """Create a standardized element entry for shot integration."""
    
    return {
        "element_id": element_data.get("character_id") or element_data.get("object_id") or element_data.get("environment_id") or element_data.get("element_id"),
        "element_name": element_data.get("character_name") or element_data.get("object_name") or element_data.get("environment_name") or element_data.get("element_name"),
        "enhanced_description": element_data.get("enhanced_description", ""),
        "position_in_shot": position,
        "visual_prominence": self._determine_visual_prominence(position),
        "original_data": element_data
    }

def _determine_visual_prominence(self, position: str) -> str:
    """Determine visual prominence based on position."""
    
    prominence_map = {
        "primary_focus": "primary_focus",
        "secondary_focus": "secondary_focus", 
        "background": "context",
        "context": "context",
        "atmospheric": "atmospheric",
        "primary_setting": "context"
    }
    
    return prominence_map.get(position, "context")
```

## Implementation Details

### Complete Component Code Structure
```python
from langflow.custom import Component
from langflow.io import DataInput, Output
from langflow.schema import Data
import json
from typing import Dict, List
from collections import defaultdict

class SceneDescriptor(Component):
    display_name = "008 Scene Descriptor"
    description = "Combine all component outputs into complete scene descriptions"
    
    inputs = [
        DataInput(name="parsed_screenplay", display_name="Parsed Screenplay"),
        DataInput(name="enhanced_descriptions", display_name="Enhanced Descriptions"),
        DataInput(name="generated_shots", display_name="Generated Shots")
    ]
    
    outputs = [
        Output(display_name="Complete Scenes", name="complete_scenes", method="describe_scenes")
    ]
    
    def describe_scenes(self) -> Data:
        """Main scene description method combining all previous outputs."""
        try:
            # Step 1: Collect and validate inputs
            inputs = self.collect_and_validate_inputs()
            
            # Step 2: Validate data consistency
            validation = self.validate_data_consistency(inputs)
            if not validation["scene_count_match"]:
                return Data(data=self._create_error_response(f"Data validation failed: {validation['issues']}"))
            
            # Step 3: Assemble complete scenes
            complete_scenes = self.assemble_complete_scenes(inputs)
            
            # Step 4: Generate assembly summary
            summary = self._generate_assembly_summary(complete_scenes, inputs)
            
            result = {
                "complete_scenes": complete_scenes,
                "assembly_summary": summary
            }
            
            return Data(data=result)
            
        except Exception as e:
            return Data(data=self._create_error_response(f"Scene description failed: {str(e)}"))
```

## Langflow Integration

### Connection Instructions
**Inputs:**
- Screenplay Parser → `parsed_screenplay`
- Description Enhancer → `enhanced_descriptions`
- Shot Generator → `generated_shots`

**Output:** `complete_scenes` → Consistency Manager

### Benefits of This Approach
- ✅ **Complete data integration** - All previous outputs combined
- ✅ **Pure Python efficiency** - No LLM overhead for data processing
- ✅ **Comprehensive scene structure** - Ready for storyboard generation
- ✅ **Consistency tracking** - Maintains visual and narrative continuity
- ✅ **Technical specifications** - Complete guidance for artists
- ✅ **No limitations** - Processes complete movies

## Next Steps
1. Connect all previous component outputs
2. Test scene assembly with your narrative
3. Verify complete data integration and consistency
4. Connect to Consistency Manager for validation
5. Continue with consistency checking phase
