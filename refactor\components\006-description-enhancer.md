# 006 Description Enhancer Component

## Overview
**Type:** LLM Component (Creative Enhancement)
**Purpose:** Enhance extracted elements with creative visual descriptions for storyboard generation
**Priority:** HIGH - Creative enhancement phase of the workflow

## LLM Usage Documentation
**When LLM is used:** For all extracted elements to add creative visual enhancement
**What LLM does:** Transforms basic extracted data into rich, creative visual descriptions suitable for storyboard generation
**Why LLM is needed:** Creative visual interpretation and artistic enhancement requires AI creativity beyond rule-based processing
**Token usage:** Moderate - processes all extracted elements but with focused, efficient prompts

## Input Specification
```python
inputs = [
    DataInput(
        name="extracted_characters", 
        display_name="Extracted Characters",
        info="Character data from character extractor"
    ),
    DataInput(
        name="extracted_objects", 
        display_name="Extracted Objects",
        info="Object data from object extractor"
    ),
    DataInput(
        name="extracted_environments", 
        display_name="Extracted Environments",
        info="Environment data from environment extractor"
    ),
    DataInput(
        name="extracted_other_elements", 
        display_name="Extracted Other Elements",
        info="Other elements data from other elements extractor"
    ),
    MessageInput(
        name="llm", 
        display_name="Language Model",
        info="Connect Gemini 2.5 for creative enhancement"
    )
]
```

## Output Specification
```json
{
  "enhanced_descriptions": {
    "characters": [
      {
        "character_id": "little_red_riding_hood",
        "character_name": "Little Red Riding Hood",
        "original_description": "young girl with red hooded cloak, carrying wicker basket",
        "enhanced_description": "A spirited young girl with flowing auburn hair peeking from beneath a vibrant crimson hooded cloak. Her bright, curious eyes sparkle with innocence and determination. She carries a carefully woven wicker basket with a checkered cloth covering, suggesting homemade treats within. Her posture shows confidence mixed with the natural caution of youth venturing into the unknown.",
        "enhancement_type": "creative_visual",
        "visual_elements": ["flowing hair", "vibrant colors", "expressive eyes", "detailed clothing", "meaningful props"],
        "artistic_notes": "Emphasize the contrast between innocence and the journey ahead",
        "storyboard_guidance": "Focus on facial expressions and body language to convey character personality"
      }
    ],
    "objects": [
      {
        "object_id": "wicker_basket",
        "object_name": "Wicker Basket",
        "original_description": "traditional woven basket with handle",
        "enhanced_description": "An intricately woven wicker basket with warm honey-colored reeds, featuring a sturdy curved handle worn smooth from use. A red and white checkered cloth peeks from beneath the lid, hinting at carefully packed homemade goods. Small details like loose weave patterns and natural imperfections give it an authentic, handcrafted appearance that speaks to family tradition and care.",
        "enhancement_type": "detailed_visual",
        "visual_elements": ["texture details", "color variations", "wear patterns", "fabric elements"],
        "artistic_notes": "Show the basket as a symbol of care and family connection",
        "storyboard_guidance": "Use close-up shots to highlight texture and craftsmanship details"
      }
    ],
    "environments": [
      {
        "environment_id": "forest_path",
        "environment_name": "Forest Path",
        "original_description": "winding dirt path through dense forest",
        "enhanced_description": "A meandering earthen path winds through an ancient forest where towering oak and pine trees create a natural cathedral. Dappled sunlight filters through the canopy, casting dancing shadows on the moss-covered ground. Wildflowers dot the path edges while ferns unfurl in the deeper shadows. The atmosphere suggests both beauty and mystery, with the path disappearing around gentle curves that invite exploration while hinting at unknown destinations.",
        "enhancement_type": "atmospheric_visual",
        "visual_elements": ["lighting effects", "natural textures", "depth layers", "atmospheric mood"],
        "artistic_notes": "Balance beauty with subtle mystery to create narrative tension",
        "storyboard_guidance": "Use perspective and lighting to guide viewer attention along the path"
      }
    ],
    "other_elements": [
      {
        "element_id": "mysterious_atmosphere",
        "element_name": "Mysterious Atmosphere",
        "original_description": "subtle shadows, dim lighting creating sense of unease and mystery",
        "enhanced_description": "An intangible but palpable sense of watchfulness permeates the scene. Shadows seem to shift slightly when not directly observed, and the quality of light takes on an almost ethereal quality. The air itself appears to hold secrets, with subtle visual cues like gently swaying branches without wind, or the way sunlight seems to avoid certain areas entirely. This creates a visual language of anticipation and hidden presence.",
        "enhancement_type": "mood_visual",
        "visual_elements": ["shadow play", "light quality", "subtle movement", "negative space"],
        "artistic_notes": "Use visual subtlety to create psychological impact",
        "storyboard_guidance": "Employ composition and framing to suggest unseen presence"
      }
    ]
  },
  "enhancement_summary": {
    "total_elements_enhanced": 45,
    "enhancement_categories": {
      "creative_visual": 15,
      "detailed_visual": 12,
      "atmospheric_visual": 10,
      "mood_visual": 8
    },
    "artistic_themes": [
      "innocence_vs_danger",
      "natural_beauty",
      "hidden_mysteries",
      "family_traditions"
    ],
    "visual_consistency_notes": "Enhanced descriptions maintain consistent artistic style and color palette",
    "storyboard_readiness": "All elements now have detailed visual guidance for artists"
  }
}
```

## Core Functionality

### 1. Element Collection and Preparation
```python
def collect_all_elements(self) -> Dict:
    """Collect all extracted elements from previous components."""
    
    all_elements = {
        "characters": [],
        "objects": [],
        "environments": [],
        "other_elements": []
    }
    
    # Collect from each extractor
    if self.extracted_characters and hasattr(self.extracted_characters, 'data'):
        characters_data = self.extracted_characters.data
        if "extracted_characters" in characters_data:
            all_elements["characters"] = characters_data["extracted_characters"]
    
    if self.extracted_objects and hasattr(self.extracted_objects, 'data'):
        objects_data = self.extracted_objects.data
        if "extracted_objects" in objects_data:
            all_elements["objects"] = objects_data["extracted_objects"]
    
    if self.extracted_environments and hasattr(self.extracted_environments, 'data'):
        environments_data = self.extracted_environments.data
        if "extracted_environments" in environments_data:
            all_elements["environments"] = environments_data["extracted_environments"]
    
    if self.extracted_other_elements and hasattr(self.extracted_other_elements, 'data'):
        other_data = self.extracted_other_elements.data
        if "extracted_other_elements" in other_data:
            all_elements["other_elements"] = other_data["extracted_other_elements"]
    
    return all_elements

def prepare_enhancement_batches(self, all_elements: Dict) -> List[Dict]:
    """Prepare elements in batches for efficient LLM processing."""
    
    batches = []
    batch_size = 5  # Process 5 elements at a time for efficiency
    
    for category, elements in all_elements.items():
        for i in range(0, len(elements), batch_size):
            batch = {
                "category": category,
                "elements": elements[i:i + batch_size],
                "batch_number": len(batches) + 1
            }
            batches.append(batch)
    
    return batches
```

### 2. Creative Enhancement with LLM
```python
def enhance_element_batch(self, batch: Dict) -> List[Dict]:
    """Enhance a batch of elements using LLM for creative visual descriptions."""
    
    category = batch["category"]
    elements = batch["elements"]
    
    # Create category-specific enhancement prompt
    prompt = self._create_enhancement_prompt(category, elements)
    
    try:
        response = self.llm.invoke(prompt)
        enhanced_elements = self._parse_enhancement_response(response, elements)
        return enhanced_elements
        
    except Exception as e:
        # Fallback: return elements with basic enhancement
        return self._create_fallback_enhancements(elements, category)

def _create_enhancement_prompt(self, category: str, elements: List[Dict]) -> str:
    """Create category-specific prompt for creative enhancement."""
    
    category_guidance = {
        "characters": {
            "focus": "personality, appearance details, clothing, expressions, body language",
            "style": "character-driven visual storytelling",
            "emphasis": "emotional connection and visual distinctiveness"
        },
        "objects": {
            "focus": "texture, materials, craftsmanship, wear patterns, symbolic meaning",
            "style": "detailed prop design",
            "emphasis": "tactile qualities and narrative significance"
        },
        "environments": {
            "focus": "atmosphere, lighting, spatial relationships, mood, scale",
            "style": "cinematic environment design",
            "emphasis": "immersive world-building and emotional tone"
        },
        "other_elements": {
            "focus": "visual representation of abstract concepts, mood, effects",
            "style": "artistic interpretation",
            "emphasis": "creative visual metaphors and atmospheric details"
        }
    }
    
    guidance = category_guidance.get(category, category_guidance["other_elements"])
    
    element_descriptions = []
    for element in elements:
        if category == "characters":
            desc = f"- {element['character_name']}: {element.get('visual_description', 'No description')}"
        elif category == "objects":
            desc = f"- {element['object_name']}: {element.get('visual_description', 'No description')}"
        elif category == "environments":
            desc = f"- {element['environment_name']}: {element.get('visual_description', 'No description')}"
        else:
            desc = f"- {element['element_name']}: {element.get('visual_description', 'No description')}"
        element_descriptions.append(desc)
    
    prompt = f"""You are a creative visual description specialist for storyboard generation.

Category: {category.title()}
Focus areas: {guidance['focus']}
Style: {guidance['style']}
Emphasis: {guidance['emphasis']}

Current {category}:
{chr(10).join(element_descriptions)}

Enhance each element with rich, creative visual descriptions suitable for storyboard artists. For each element, provide:

1. Enhanced visual description (3-4 sentences with specific visual details)
2. Key visual elements (list of 4-5 specific visual components)
3. Artistic notes (guidance for visual style and mood)
4. Storyboard guidance (specific direction for artists)

Focus on:
- Specific visual details that artists can implement
- Consistent artistic style across all elements
- Creative enhancement while maintaining story authenticity
- Clear guidance for storyboard composition and framing

Respond with JSON format:
{{
  "enhanced_elements": [
    {{
      "element_index": 0,
      "enhanced_description": "detailed creative description here",
      "visual_elements": ["element1", "element2", "element3", "element4"],
      "artistic_notes": "style and mood guidance",
      "storyboard_guidance": "specific direction for artists"
    }}
  ]
}}"""

    return prompt
```

## Implementation Details

### Complete Component Code Structure
```python
from langflow.custom import Component
from langflow.io import DataInput, Output
from langflow.inputs import MessageInput
from langflow.schema import Data
import json
from typing import Dict, List

class DescriptionEnhancer(Component):
    display_name = "006 Description Enhancer"
    description = "Enhance extracted elements with creative visual descriptions"
    
    inputs = [
        DataInput(name="extracted_characters", display_name="Extracted Characters"),
        DataInput(name="extracted_objects", display_name="Extracted Objects"),
        DataInput(name="extracted_environments", display_name="Extracted Environments"),
        DataInput(name="extracted_other_elements", display_name="Extracted Other Elements"),
        MessageInput(name="llm", display_name="Language Model")
    ]
    
    outputs = [
        Output(display_name="Enhanced Descriptions", name="enhanced_descriptions", method="enhance_descriptions")
    ]
    
    def enhance_descriptions(self) -> Data:
        """Main enhancement method using LLM for creative visual descriptions."""
        try:
            # Step 1: Collect all elements
            all_elements = self.collect_all_elements()
            
            if not any(all_elements.values()):
                return Data(data=self._create_error_response("No elements to enhance"))
            
            # Step 2: Prepare batches for efficient processing
            batches = self.prepare_enhancement_batches(all_elements)
            
            # Step 3: Enhance each batch with LLM
            enhanced_data = {"characters": [], "objects": [], "environments": [], "other_elements": []}
            
            for batch in batches:
                enhanced_batch = self.enhance_element_batch(batch)
                enhanced_data[batch["category"]].extend(enhanced_batch)
            
            # Step 4: Create summary
            summary = self._create_enhancement_summary(enhanced_data)
            
            result = {
                "enhanced_descriptions": enhanced_data,
                "enhancement_summary": summary
            }
            
            return Data(data=result)
            
        except Exception as e:
            return Data(data=self._create_error_response(f"Description enhancement failed: {str(e)}"))
```

## Langflow Integration

### Connection Instructions
**Inputs:**
- Character Extractor → `extracted_characters`
- Object Extractor → `extracted_objects`
- Environment Extractor → `extracted_environments`
- Other Elements Extractor → `extracted_other_elements`
- Language Model (Gemini) → `llm`

**Output:** `enhanced_descriptions` → Shot Generator

### Benefits of This Approach
- ✅ **Creative enhancement** - LLM adds artistic visual details
- ✅ **Comprehensive processing** - Handles all element types
- ✅ **Efficient batching** - Optimizes LLM usage
- ✅ **Artist-ready output** - Detailed guidance for storyboard creation
- ✅ **Consistent style** - Maintains visual coherence across elements
- ✅ **No limitations** - Processes complete movies

## Next Steps
1. Connect all extractor outputs to this component
2. Test creative enhancement with your narrative
3. Verify artistic consistency across all elements
4. Connect to Shot Generator for cinematic composition
5. Continue with Director of Photography phase
