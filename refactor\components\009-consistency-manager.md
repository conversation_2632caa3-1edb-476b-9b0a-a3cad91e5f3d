# 009 Consistency Manager Component

## Overview
**Type:** Pure Python Component (Consistency Validation)
**Purpose:** Ensure visual and narrative consistency across all scenes for professional storyboard generation
**Priority:** HIGH - Quality assurance phase of the workflow

## LLM Usage Documentation
**When LLM is used:** Never - This component uses only Python analysis and validation
**What LLM does:** N/A - Pure Python component
**Why LLM is not needed:** Consistency checking can be handled efficiently with Python logic and pattern matching
**Token usage:** None - No LLM calls

## Input Specification
```python
inputs = [
    DataInput(
        name="complete_scenes", 
        display_name="Complete Scenes",
        info="Complete scene descriptions from scene descriptor"
    )
]
```

## Output Specification
```json
{
  "consistency_report": {
    "overall_consistency_score": 0.92,
    "consistency_status": "GOOD",
    "validation_categories": {
      "character_consistency": {
        "score": 0.95,
        "status": "EXCELLENT",
        "issues_found": 0,
        "validations_performed": 12
      },
      "object_consistency": {
        "score": 0.88,
        "status": "GOOD", 
        "issues_found": 2,
        "validations_performed": 18
      },
      "environment_consistency": {
        "score": 0.94,
        "status": "EXCELLENT",
        "issues_found": 1,
        "validations_performed": 8
      },
      "lighting_consistency": {
        "score": 0.91,
        "status": "GOOD",
        "issues_found": 1,
        "validations_performed": 24
      },
      "cinematography_consistency": {
        "score": 0.89,
        "status": "GOOD",
        "issues_found": 2,
        "validations_performed": 24
      }
    },
    "detailed_issues": [
      {
        "issue_id": "obj_001",
        "category": "object_consistency",
        "severity": "medium",
        "description": "Wicker basket description varies between scenes 2 and 4",
        "affected_scenes": [2, 4],
        "affected_shots": ["shot_005", "shot_012"],
        "current_descriptions": [
          "Small wicker basket with red cloth",
          "Large wicker basket with checkered cloth"
        ],
        "recommended_resolution": "Standardize basket size as 'medium' and cloth as 'red and white checkered'",
        "auto_fix_available": true
      }
    ],
    "consistency_improvements": [
      {
        "improvement_id": "imp_001",
        "category": "character_consistency",
        "description": "Little Red Riding Hood's cloak color consistently described as 'vibrant crimson'",
        "affected_scenes": [1, 2, 3, 4, 5],
        "consistency_strength": "excellent"
      }
    ]
  },
  "validated_scenes": [
    {
      "scene_id": "scene_001",
      "scene_number": 1,
      "consistency_score": 0.96,
      "validation_status": "PASSED",
      "issues_found": 0,
      "consistency_notes": {
        "character_consistency": "All character descriptions match established baseline",
        "object_consistency": "All objects properly described and tracked",
        "environment_consistency": "Environment descriptions consistent with scene requirements",
        "lighting_consistency": "Lighting matches time of day and mood requirements"
      },
      "validated_elements": {
        "characters": ["little_red_riding_hood"],
        "objects": ["wicker_basket", "cottage_door"],
        "environments": ["cottage_exterior", "garden_path"],
        "lighting_setups": ["natural_morning_light"]
      }
    }
  ],
  "consistency_summary": {
    "total_scenes_validated": 8,
    "total_shots_validated": 24,
    "total_elements_tracked": 45,
    "consistency_metrics": {
      "character_appearances": 12,
      "object_continuity_checks": 18,
      "environment_transitions": 6,
      "lighting_validations": 24,
      "cinematography_reviews": 24
    },
    "quality_assurance": {
      "ready_for_storyboard": true,
      "requires_manual_review": false,
      "auto_fixes_applied": 3,
      "manual_fixes_needed": 1
    }
  }
}
```

## Core Functionality

### 1. Character Consistency Validation
```python
def validate_character_consistency(self, complete_scenes: List[Dict]) -> Dict:
    """Validate character appearance and description consistency across scenes."""
    
    character_tracker = {}
    issues = []
    validations = 0
    
    # Build character baseline from first appearances
    for scene in complete_scenes:
        for shot in scene.get("shots", []):
            for character in shot.get("elements", {}).get("characters", []):
                char_id = character["element_id"]
                char_name = character["element_name"]
                
                if char_id not in character_tracker:
                    # Establish baseline
                    character_tracker[char_id] = {
                        "name": char_name,
                        "baseline_description": character["enhanced_description"],
                        "appearances": [],
                        "description_variations": []
                    }
                
                # Track this appearance
                appearance = {
                    "scene_number": scene["scene_number"],
                    "shot_id": shot["shot_id"],
                    "description": character["enhanced_description"]
                }
                character_tracker[char_id]["appearances"].append(appearance)
                validations += 1
    
    # Validate consistency
    for char_id, char_data in character_tracker.items():
        baseline = char_data["baseline_description"]
        
        for appearance in char_data["appearances"]:
            consistency_score = self._calculate_description_similarity(baseline, appearance["description"])
            
            if consistency_score < 0.8:  # Threshold for consistency
                issues.append({
                    "issue_id": f"char_{char_id}_{appearance['scene_number']}",
                    "category": "character_consistency",
                    "severity": "medium" if consistency_score > 0.6 else "high",
                    "description": f"Character {char_data['name']} description inconsistent in scene {appearance['scene_number']}",
                    "affected_scenes": [appearance["scene_number"]],
                    "affected_shots": [appearance["shot_id"]],
                    "current_descriptions": [baseline, appearance["description"]],
                    "recommended_resolution": f"Align description with baseline: {baseline[:100]}...",
                    "auto_fix_available": True,
                    "consistency_score": consistency_score
                })
    
    score = max(0, 1 - (len(issues) / max(validations, 1)))
    status = self._determine_status(score)
    
    return {
        "score": score,
        "status": status,
        "issues_found": len(issues),
        "validations_performed": validations,
        "issues": issues,
        "character_tracker": character_tracker
    }

def _calculate_description_similarity(self, baseline: str, comparison: str) -> float:
    """Calculate similarity between two descriptions using keyword matching."""
    
    # Extract key visual elements
    baseline_keywords = self._extract_visual_keywords(baseline)
    comparison_keywords = self._extract_visual_keywords(comparison)
    
    if not baseline_keywords:
        return 1.0  # No baseline to compare
    
    # Calculate overlap
    common_keywords = baseline_keywords.intersection(comparison_keywords)
    similarity = len(common_keywords) / len(baseline_keywords)
    
    return similarity

def _extract_visual_keywords(self, description: str) -> set:
    """Extract key visual descriptors from a description."""
    
    import re
    
    # Key visual categories
    visual_patterns = [
        r'\b(red|blue|green|yellow|black|white|brown|gray|crimson|auburn|golden)\b',  # Colors
        r'\b(tall|short|small|large|tiny|huge|medium|long|flowing|curly|straight)\b',  # Sizes/shapes
        r'\b(cloak|dress|shirt|pants|hat|hood|basket|door|window|tree|path)\b',  # Objects
        r'\b(bright|dark|dim|glowing|sparkling|shadowy|luminous)\b',  # Lighting
        r'\b(young|old|elderly|child|adult|innocent|wise|curious)\b'  # Age/traits
    ]
    
    keywords = set()
    description_lower = description.lower()
    
    for pattern in visual_patterns:
        matches = re.findall(pattern, description_lower)
        keywords.update(matches)
    
    return keywords
```

### 2. Object and Environment Consistency
```python
def validate_object_consistency(self, complete_scenes: List[Dict]) -> Dict:
    """Validate object appearance and properties consistency."""
    
    object_tracker = {}
    issues = []
    validations = 0
    
    # Track all object appearances
    for scene in complete_scenes:
        for shot in scene.get("shots", []):
            for obj in shot.get("elements", {}).get("objects", []):
                obj_id = obj["element_id"]
                
                if obj_id not in object_tracker:
                    object_tracker[obj_id] = {
                        "name": obj["element_name"],
                        "baseline_description": obj["enhanced_description"],
                        "appearances": []
                    }
                
                object_tracker[obj_id]["appearances"].append({
                    "scene_number": scene["scene_number"],
                    "shot_id": shot["shot_id"],
                    "description": obj["enhanced_description"],
                    "position": obj["position_in_shot"]
                })
                validations += 1
    
    # Validate consistency
    for obj_id, obj_data in object_tracker.items():
        baseline = obj_data["baseline_description"]
        
        for appearance in obj_data["appearances"]:
            consistency_score = self._calculate_description_similarity(baseline, appearance["description"])
            
            if consistency_score < 0.75:  # Objects need higher consistency
                issues.append({
                    "issue_id": f"obj_{obj_id}_{appearance['scene_number']}",
                    "category": "object_consistency",
                    "severity": "medium",
                    "description": f"Object {obj_data['name']} description inconsistent",
                    "affected_scenes": [appearance["scene_number"]],
                    "affected_shots": [appearance["shot_id"]],
                    "current_descriptions": [baseline, appearance["description"]],
                    "recommended_resolution": f"Standardize object description",
                    "auto_fix_available": True
                })
    
    score = max(0, 1 - (len(issues) / max(validations, 1)))
    status = self._determine_status(score)
    
    return {
        "score": score,
        "status": status,
        "issues_found": len(issues),
        "validations_performed": validations,
        "issues": issues
    }

def validate_environment_consistency(self, complete_scenes: List[Dict]) -> Dict:
    """Validate environment descriptions and transitions."""
    
    environment_tracker = {}
    issues = []
    validations = 0
    
    # Track environment appearances and transitions
    previous_environment = None
    
    for scene in complete_scenes:
        scene_environments = set()
        
        for shot in scene.get("shots", []):
            for env in shot.get("elements", {}).get("environments", []):
                env_id = env["element_id"]
                scene_environments.add(env_id)
                
                if env_id not in environment_tracker:
                    environment_tracker[env_id] = {
                        "name": env["element_name"],
                        "baseline_description": env["enhanced_description"],
                        "scenes": []
                    }
                
                if scene["scene_number"] not in environment_tracker[env_id]["scenes"]:
                    environment_tracker[env_id]["scenes"].append(scene["scene_number"])
                
                validations += 1
        
        # Check for abrupt environment changes
        if previous_environment and scene_environments:
            if not scene_environments.intersection(previous_environment):
                # Complete environment change - check if transition is logical
                transition_score = self._validate_environment_transition(previous_environment, scene_environments)
                if transition_score < 0.7:
                    issues.append({
                        "issue_id": f"env_transition_{scene['scene_number']}",
                        "category": "environment_consistency",
                        "severity": "low",
                        "description": f"Abrupt environment transition in scene {scene['scene_number']}",
                        "affected_scenes": [scene["scene_number"]],
                        "recommended_resolution": "Consider adding transition shot or explanation",
                        "auto_fix_available": False
                    })
        
        previous_environment = scene_environments
    
    score = max(0, 1 - (len(issues) / max(validations, 1)))
    status = self._determine_status(score)
    
    return {
        "score": score,
        "status": status,
        "issues_found": len(issues),
        "validations_performed": validations,
        "issues": issues
    }
```

### 3. Technical Consistency Validation
```python
def validate_lighting_consistency(self, complete_scenes: List[Dict]) -> Dict:
    """Validate lighting consistency within scenes and across time periods."""
    
    issues = []
    validations = 0
    
    for scene in complete_scenes:
        scene_time = scene.get("time", "").lower()
        expected_lighting = self._determine_expected_lighting(scene_time)
        
        scene_lighting = []
        
        for shot in scene.get("shots", []):
            lighting = shot.get("technical_specs", {}).get("lighting_direction", "")
            scene_lighting.append(lighting)
            validations += 1
            
            # Check if lighting matches time of day
            if not self._lighting_matches_time(lighting, scene_time):
                issues.append({
                    "issue_id": f"light_{scene['scene_number']}_{shot['shot_number']}",
                    "category": "lighting_consistency",
                    "severity": "medium",
                    "description": f"Lighting '{lighting}' inconsistent with time '{scene_time}'",
                    "affected_scenes": [scene["scene_number"]],
                    "affected_shots": [shot["shot_id"]],
                    "recommended_resolution": f"Use {expected_lighting} lighting for {scene_time}",
                    "auto_fix_available": True
                })
        
        # Check lighting consistency within scene
        if len(set(scene_lighting)) > 2:  # Allow some variation but not too much
            issues.append({
                "issue_id": f"light_scene_{scene['scene_number']}",
                "category": "lighting_consistency",
                "severity": "low",
                "description": f"Too much lighting variation within scene {scene['scene_number']}",
                "affected_scenes": [scene["scene_number"]],
                "recommended_resolution": "Maintain more consistent lighting within scene",
                "auto_fix_available": False
            })
    
    score = max(0, 1 - (len(issues) / max(validations, 1)))
    status = self._determine_status(score)
    
    return {
        "score": score,
        "status": status,
        "issues_found": len(issues),
        "validations_performed": validations,
        "issues": issues
    }

def validate_cinematography_consistency(self, complete_scenes: List[Dict]) -> Dict:
    """Validate cinematography style and technical consistency."""
    
    issues = []
    validations = 0
    
    # Track cinematography patterns
    shot_types = []
    camera_movements = []
    visual_styles = []
    
    for scene in complete_scenes:
        for shot in scene.get("shots", []):
            shot_types.append(shot.get("shot_type", ""))
            camera_movements.append(shot.get("camera_movement", ""))
            visual_styles.append(shot.get("technical_specs", {}).get("visual_style", ""))
            validations += 1
    
    # Check for reasonable shot type distribution
    shot_type_distribution = self._calculate_distribution(shot_types)
    if shot_type_distribution.get("extreme_close_up", 0) > 0.3:  # Too many extreme close-ups
        issues.append({
            "issue_id": "cinema_shot_distribution",
            "category": "cinematography_consistency",
            "severity": "low",
            "description": "Excessive use of extreme close-ups may impact visual flow",
            "recommended_resolution": "Balance shot types for better visual rhythm",
            "auto_fix_available": False
        })
    
    # Check for excessive camera movement
    movement_distribution = self._calculate_distribution(camera_movements)
    if movement_distribution.get("static", 0) < 0.4:  # Too much movement
        issues.append({
            "issue_id": "cinema_movement_excess",
            "category": "cinematography_consistency", 
            "severity": "medium",
            "description": "Excessive camera movement may cause visual fatigue",
            "recommended_resolution": "Include more static shots for visual stability",
            "auto_fix_available": False
        })
    
    score = max(0, 1 - (len(issues) / max(validations, 1)))
    status = self._determine_status(score)
    
    return {
        "score": score,
        "status": status,
        "issues_found": len(issues),
        "validations_performed": validations,
        "issues": issues
    }
```

## Implementation Details

### Complete Component Code Structure
```python
from langflow.custom import Component
from langflow.io import DataInput, Output
from langflow.schema import Data
import json
from typing import Dict, List
from collections import Counter

class ConsistencyManager(Component):
    display_name = "009 Consistency Manager"
    description = "Ensure visual and narrative consistency across all scenes"
    
    inputs = [
        DataInput(name="complete_scenes", display_name="Complete Scenes")
    ]
    
    outputs = [
        Output(display_name="Consistency Report", name="consistency_report", method="validate_consistency")
    ]
    
    def validate_consistency(self) -> Data:
        """Main consistency validation method using Python analysis."""
        try:
            complete_scenes_data = self.complete_scenes.data
            
            if not complete_scenes_data or "complete_scenes" not in complete_scenes_data:
                return Data(data=self._create_error_response("No complete scenes provided"))
            
            scenes = complete_scenes_data["complete_scenes"]
            
            # Perform all consistency validations
            character_validation = self.validate_character_consistency(scenes)
            object_validation = self.validate_object_consistency(scenes)
            environment_validation = self.validate_environment_consistency(scenes)
            lighting_validation = self.validate_lighting_consistency(scenes)
            cinematography_validation = self.validate_cinematography_consistency(scenes)
            
            # Calculate overall consistency score
            overall_score = self._calculate_overall_score([
                character_validation, object_validation, environment_validation,
                lighting_validation, cinematography_validation
            ])
            
            # Compile all issues
            all_issues = []
            all_issues.extend(character_validation.get("issues", []))
            all_issues.extend(object_validation.get("issues", []))
            all_issues.extend(environment_validation.get("issues", []))
            all_issues.extend(lighting_validation.get("issues", []))
            all_issues.extend(cinematography_validation.get("issues", []))
            
            # Apply auto-fixes where possible
            validated_scenes = self._apply_auto_fixes(scenes, all_issues)
            
            # Generate final report
            result = {
                "consistency_report": {
                    "overall_consistency_score": overall_score,
                    "consistency_status": self._determine_status(overall_score),
                    "validation_categories": {
                        "character_consistency": character_validation,
                        "object_consistency": object_validation,
                        "environment_consistency": environment_validation,
                        "lighting_consistency": lighting_validation,
                        "cinematography_consistency": cinematography_validation
                    },
                    "detailed_issues": all_issues,
                    "consistency_improvements": self._identify_improvements(scenes)
                },
                "validated_scenes": validated_scenes,
                "consistency_summary": self._generate_consistency_summary(scenes, all_issues)
            }
            
            return Data(data=result)
            
        except Exception as e:
            return Data(data=self._create_error_response(f"Consistency validation failed: {str(e)}"))
```

## Langflow Integration

### Connection Instructions
**Inputs:**
- Scene Descriptor → `complete_scenes`

**Output:** `consistency_report` → Storyboard Optimizer

### Benefits of This Approach
- ✅ **Comprehensive validation** - All consistency aspects covered
- ✅ **Pure Python efficiency** - No LLM overhead for validation
- ✅ **Automated issue detection** - Systematic consistency checking
- ✅ **Auto-fix capabilities** - Resolves simple consistency issues
- ✅ **Quality assurance** - Professional storyboard standards
- ✅ **No limitations** - Validates complete movies

## Next Steps
1. Connect complete scenes to this component
2. Test consistency validation with your narrative
3. Review and resolve any consistency issues found
4. Connect to Storyboard Optimizer for final processing
5. Complete the workflow with final optimization
