flowchart LR
    %% Input
    FILE[File<br/>Narrative Text] --> SP_input[narrative_text]

    %% Component 1: Screenplay Parser (Pure Python)
    SP_input --> SP[Python Component<br/>001 Screenplay Parser<br/>🐍 Pure Python]
    SP --> SP_out[parsed_screenplay]

    %% Component 2: Character Extractor (Minimal LLM)
    SP_out --> CE_parsed[parsed_screenplay]
    CE_parsed --> CE[Python Component<br/>002 Character Extractor<br/>🐍 Minimal LLM Usage]
    LM1[Language Model<br/>Gemini 2.5] --> CE_llm[llm]
    CE_llm --> CE
    CE --> CE_out[extracted_characters]

    %% Component 3: Object Extractor (Minimal LLM)
    SP_out --> OE_parsed[parsed_screenplay]
    OE_parsed --> OE[Python Component<br/>003 Object Extractor<br/>🐍 Minimal LLM Usage]
    LM1 --> OE_llm[llm]
    OE_llm --> OE
    OE --> OE_out[extracted_objects]

    %% Component 4: Environment Extractor (Minimal LLM)
    SP_out --> EE_parsed[parsed_screenplay]
    EE_parsed --> EE[Python Component<br/>004 Environment Extractor<br/>🐍 Minimal LLM Usage]
    LM1 --> EE_llm[llm]
    EE_llm --> EE
    EE --> EE_out[extracted_environments]

    %% Component 5: Other Elements Extractor (Minimal LLM)
    SP_out --> OTE_parsed[parsed_screenplay]
    OTE_parsed --> OTE[Python Component<br/>005 Other Elements Extractor<br/>🐍 Minimal LLM Usage]
    LM1 --> OTE_llm[llm]
    OTE_llm --> OTE
    OTE --> OTE_out[extracted_other_elements]
    
    %% Component 6: Description Enhancer (Python + LLM for Creative Enhancement)
    CE_out --> DE_characters[extracted_characters]
    OE_out --> DE_objects[extracted_objects]
    EE_out --> DE_environments[extracted_environments]
    OTE_out --> DE_other[extracted_other_elements]
    DE_characters --> DE[Python Component<br/>006 Description Enhancer<br/>🐍 Python + AI for Creative Enhancement]
    DE_objects --> DE
    DE_environments --> DE
    DE_other --> DE
    LM1[Language Model<br/>Gemini 2.5] --> DE_llm[llm]
    DE_llm --> DE
    DE --> DE_out[enhanced_descriptions]

    %% Component 7: Shot Generator (Director of Photography LLM)
    DE_out --> SG_enhanced[enhanced_descriptions]
    SG_enhanced --> SG[Python Component<br/>007 Shot Generator<br/>🎬 Director of Photography]
    LM1 --> SG_llm[llm]
    SG_llm --> SG
    SG --> SG_out[generated_shots]
    
    %% Component 8: Scene Descriptor (Pure Python)
    SP_out --> SD_parsed[parsed_screenplay]
    DE_out --> SD_enhanced[enhanced_descriptions]
    SG_out --> SD_shots[generated_shots]
    SD_parsed --> SD[Python Component<br/>008 Scene Descriptor<br/>🐍 Pure Python]
    SD_enhanced --> SD
    SD_shots --> SD
    SD --> SD_out[complete_scenes]

    %% Component 9: Consistency Manager (Pure Python)
    SD_out --> CM_scenes[complete_scenes]
    CM_scenes --> CM[Python Component<br/>009 Consistency Manager<br/>🐍 Pure Python]
    CM --> CM_out[consistency_report]

    %% Component 10: Storyboard Optimizer (User-Defined Character Limits)
    CM_out --> SO_report[consistency_report]
    SO_report --> SO[Python Component<br/>010 Storyboard Optimizer<br/>🎯 User-Defined Character Limits]
    LM1 --> SO_llm[llm]
    SO_llm --> SO

    %% User Configuration Inputs
    MAX_LENGTH[User Input<br/>Max Prompt Length<br/>Default: 500] --> SO_max[max_prompt_length]
    MIN_LENGTH[User Input<br/>Min Prompt Length<br/>Default: 200] --> SO_min[min_prompt_length]
    FOCUS[User Input<br/>Optimization Focus<br/>Default: balanced] --> SO_focus[optimization_focus]

    SO_max --> SO
    SO_min --> SO
    SO_focus --> SO
    SO --> FINAL[optimized_storyboard<br/>🎬 Ready for Video Generation]
    
    %% Styling
    classDef file fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef userinput fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef llm fill:#e1f5fe,stroke:#2196f3,stroke-width:2px
    classDef python fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef pythonai fill:#fff8e1,stroke:#ffc107,stroke-width:3px
    classDef io fill:#ffecb3,stroke:#795548,stroke-width:1px
    classDef final fill:#e8f5e8,stroke:#4caf50,stroke-width:3px

    class FILE file
    class MAX_LENGTH,MIN_LENGTH,FOCUS userinput
    class LM1 llm
    class SP,SD,CM python
    class CE,OE,EE,OTE,DE,SG,SO pythonai
    class SP_input,SP_out,CE_parsed,CE_out,OE_parsed,OE_out,EE_parsed,EE_out,OTE_parsed,OTE_out,DE_characters,DE_objects,DE_environments,DE_other,DE_out,SG_enhanced,SG_out,SD_parsed,SD_enhanced,SD_shots,SD_out,CM_scenes,CM_out,SO_report,CE_llm,OE_llm,EE_llm,OTE_llm,DE_llm,SG_llm,SO_llm,SO_max,SO_min,SO_focus io
    class FINAL final
