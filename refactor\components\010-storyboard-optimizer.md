# 010 Storyboard Optimizer Component

## Overview
**Type:** LLM Component (Final Optimization)
**Purpose:** Generate final optimized storyboard prompts with user-defined character limits for video generation
**Priority:** HIGH - Final output phase of the workflow

## LLM Usage Documentation
**When LLM is used:** For final optimization and formatting of storyboard prompts
**What LLM does:** Optimizes complete scene data into concise, effective video generation prompts within user-specified character limits
**Why LLM is needed:** Requires creative synthesis and optimization to distill complex scene data into effective prompts
**Token usage:** Moderate - processes validated scenes with optimization prompts

## Input Specification
```python
inputs = [
    DataInput(
        name="consistency_report", 
        display_name="Consistency Report",
        info="Validated scenes and consistency data from consistency manager"
    ),
    IntInput(
        name="max_prompt_length",
        display_name="Maximum Prompt Length",
        info="User-defined character limit for each storyboard prompt (default: 500)",
        value=500
    ),
    IntInput(
        name="min_prompt_length",
        display_name="Minimum Prompt Length", 
        info="User-defined minimum character count for prompts (default: 200)",
        value=200
    ),
    DropdownInput(
        name="optimization_focus",
        display_name="Optimization Focus",
        info="Focus area for prompt optimization",
        options=["visual_detail", "narrative_flow", "technical_precision", "artistic_style", "balanced"],
        value="balanced"
    ),
    MessageInput(
        name="llm", 
        display_name="Language Model",
        info="Connect Gemini 2.5 for final prompt optimization"
    )
]
```

## Output Specification
```json
{
  "optimized_storyboard": {
    "storyboard_metadata": {
      "total_scenes": 8,
      "total_shots": 24,
      "total_prompts": 24,
      "user_settings": {
        "max_prompt_length": 500,
        "min_prompt_length": 200,
        "optimization_focus": "balanced"
      },
      "optimization_summary": {
        "prompts_within_limits": 24,
        "prompts_requiring_truncation": 0,
        "average_prompt_length": 387,
        "optimization_success": "excellent"
      }
    },
    "storyboard_prompts": [
      {
        "prompt_id": "prompt_001",
        "scene_number": 1,
        "shot_number": 1,
        "shot_id": "shot_001_little_red_introduction",
        "optimized_prompt": "Medium shot of a spirited young girl with flowing auburn hair beneath a vibrant crimson hooded cloak. She stands at a charming cottage door, holding an intricately woven honey-colored wicker basket with red and white checkered cloth. Soft morning light illuminates her bright, curious eyes and innocent expression. The cottage's warm architecture and garden path leading to the forest are visible in the background. Camera at eye level, static composition using rule of thirds with character positioned on right third. Natural front lighting creates gentle shadows, emphasizing warmth and innocence before her journey begins.",
        "character_count": 487,
        "within_limits": true,
        "optimization_notes": "Balanced visual detail with narrative context, maintained technical specifications",
        "key_elements_preserved": ["character_appearance", "basket_details", "lighting_mood", "camera_position", "narrative_context"],
        "technical_specs": {
          "shot_type": "medium_shot",
          "camera_angle": "eye_level",
          "lighting": "natural_front_lighting",
          "composition": "rule_of_thirds"
        },
        "video_generation_tags": ["character_introduction", "cottage_setting", "morning_light", "innocent_mood", "journey_beginning"]
      }
    ]
  },
  "optimization_report": {
    "processing_summary": {
      "scenes_processed": 8,
      "shots_optimized": 24,
      "consistency_issues_resolved": 4,
      "character_limit_compliance": "100%"
    },
    "optimization_metrics": {
      "visual_detail_preservation": 0.92,
      "narrative_coherence": 0.95,
      "technical_accuracy": 0.89,
      "prompt_efficiency": 0.91,
      "user_requirement_compliance": 1.0
    },
    "quality_indicators": {
      "prompts_ready_for_video_generation": 24,
      "manual_review_recommended": 0,
      "optimization_success_rate": 1.0
    }
  }
}
```

## Core Functionality

### 1. User Settings Validation and Processing
```python
def validate_user_settings(self) -> Dict:
    """Validate and process user-defined settings for optimization."""
    
    settings = {
        "max_prompt_length": self.max_prompt_length,
        "min_prompt_length": self.min_prompt_length,
        "optimization_focus": self.optimization_focus
    }
    
    # Validate character limits
    if settings["max_prompt_length"] < 100:
        settings["max_prompt_length"] = 100  # Minimum viable prompt length
    if settings["max_prompt_length"] > 2000:
        settings["max_prompt_length"] = 2000  # Maximum reasonable prompt length
    
    if settings["min_prompt_length"] < 50:
        settings["min_prompt_length"] = 50
    if settings["min_prompt_length"] >= settings["max_prompt_length"]:
        settings["min_prompt_length"] = settings["max_prompt_length"] - 50
    
    return settings

def extract_validated_scenes(self, consistency_data: Dict) -> List[Dict]:
    """Extract validated scenes from consistency report."""
    
    if "validated_scenes" in consistency_data:
        return consistency_data["validated_scenes"]
    elif "consistency_report" in consistency_data and "validated_scenes" in consistency_data["consistency_report"]:
        return consistency_data["consistency_report"]["validated_scenes"]
    else:
        raise ValueError("No validated scenes found in consistency data")
```

### 2. Prompt Optimization with Character Limits
```python
def optimize_scene_prompts(self, validated_scenes: List[Dict], settings: Dict) -> List[Dict]:
    """Optimize all scene shots into prompts within user-defined character limits."""
    
    optimized_prompts = []
    
    # Process scenes in batches for efficiency
    for scene in validated_scenes:
        scene_prompts = self._optimize_scene_shots(scene, settings)
        optimized_prompts.extend(scene_prompts)
    
    return optimized_prompts

def _optimize_scene_shots(self, scene: Dict, settings: Dict) -> List[Dict]:
    """Optimize all shots in a scene into prompts."""
    
    scene_prompts = []
    shots = scene.get("shots", [])
    
    # Process shots in small batches
    batch_size = 3
    for i in range(0, len(shots), batch_size):
        batch = shots[i:i + batch_size]
        batch_prompts = self._optimize_shot_batch(batch, scene, settings)
        scene_prompts.extend(batch_prompts)
    
    return scene_prompts

def _optimize_shot_batch(self, shots: List[Dict], scene: Dict, settings: Dict) -> List[Dict]:
    """Optimize a batch of shots using LLM with character limit constraints."""
    
    prompt = self._create_optimization_prompt(shots, scene, settings)
    
    try:
        response = self.llm.invoke(prompt)
        optimized_prompts = self._parse_optimization_response(response, shots, settings)
        return optimized_prompts
        
    except Exception as e:
        # Fallback: create basic prompts within limits
        return self._create_fallback_prompts(shots, settings)

def _create_optimization_prompt(self, shots: List[Dict], scene: Dict, settings: Dict) -> str:
    """Create optimization prompt with user-defined character limits."""
    
    max_length = settings["max_prompt_length"]
    min_length = settings["min_prompt_length"]
    focus = settings["optimization_focus"]
    
    focus_guidance = {
        "visual_detail": "Prioritize rich visual descriptions, textures, colors, and artistic details",
        "narrative_flow": "Emphasize story progression, character emotions, and narrative context",
        "technical_precision": "Focus on camera work, lighting, composition, and technical specifications",
        "artistic_style": "Highlight artistic style, mood, atmosphere, and creative visual elements",
        "balanced": "Balance visual detail, narrative context, and technical specifications equally"
    }
    
    shot_data = []
    for i, shot in enumerate(shots):
        elements_summary = self._summarize_shot_elements(shot)
        technical_summary = self._summarize_technical_specs(shot)
        
        shot_info = f"""Shot {i+1}:
- ID: {shot.get('shot_id', 'unknown')}
- Type: {shot.get('shot_type', 'medium_shot')}
- Description: {shot.get('shot_description', '')[:200]}...
- Elements: {elements_summary}
- Technical: {technical_summary}"""
        
        shot_data.append(shot_info)
    
    prompt = f"""You are a video generation prompt optimization specialist.

CRITICAL REQUIREMENTS:
- Each prompt must be between {min_length} and {max_length} characters
- Optimization focus: {focus} - {focus_guidance[focus]}
- Prompts are for video generation, not static images
- Include essential visual, narrative, and technical elements
- Maintain professional storyboard quality

Scene Context:
- Scene {scene.get('scene_number', 1)}: {scene.get('scene_title', 'Unknown')}
- Location: {scene.get('location', 'Unknown')}
- Time: {scene.get('time', 'Unknown')}

Shots to optimize:
{chr(10).join(shot_data)}

For each shot, create an optimized video generation prompt that:
1. Stays within {min_length}-{max_length} character limit
2. Includes key visual elements (characters, objects, environment)
3. Specifies camera work and composition
4. Maintains narrative context
5. Uses efficient, descriptive language
6. Focuses on {focus}

Respond with JSON:
{{
  "optimized_prompts": [
    {{
      "shot_index": 0,
      "optimized_prompt": "Complete optimized prompt within character limits",
      "character_count": 487,
      "key_elements_preserved": ["element1", "element2", "element3"],
      "optimization_notes": "Brief explanation of optimization choices"
    }}
  ]
}}"""

    return prompt

def _summarize_shot_elements(self, shot: Dict) -> str:
    """Create concise summary of shot elements."""
    
    elements = shot.get("elements", {})
    summary_parts = []
    
    if elements.get("characters"):
        char_names = [c.get("element_name", "Unknown") for c in elements["characters"]]
        summary_parts.append(f"Characters: {', '.join(char_names)}")
    
    if elements.get("objects"):
        obj_names = [o.get("element_name", "Unknown") for o in elements["objects"]]
        summary_parts.append(f"Objects: {', '.join(obj_names)}")
    
    if elements.get("environments"):
        env_names = [e.get("element_name", "Unknown") for e in elements["environments"]]
        summary_parts.append(f"Environment: {', '.join(env_names)}")
    
    return "; ".join(summary_parts) if summary_parts else "No specific elements"

def _summarize_technical_specs(self, shot: Dict) -> str:
    """Create concise summary of technical specifications."""
    
    specs = shot.get("technical_specs", {})
    tech_parts = []
    
    if specs.get("lighting_direction"):
        tech_parts.append(f"Lighting: {specs['lighting_direction']}")
    if specs.get("visual_style"):
        tech_parts.append(f"Style: {specs['visual_style']}")
    
    camera_info = f"{shot.get('shot_type', 'medium')}, {shot.get('camera_angle', 'eye_level')}"
    tech_parts.append(f"Camera: {camera_info}")
    
    return "; ".join(tech_parts)
```

### 3. Character Limit Validation and Adjustment
```python
def validate_prompt_lengths(self, optimized_prompts: List[Dict], settings: Dict) -> List[Dict]:
    """Validate and adjust prompts to meet user-defined character limits."""
    
    max_length = settings["max_prompt_length"]
    min_length = settings["min_prompt_length"]
    
    validated_prompts = []
    
    for prompt_data in optimized_prompts:
        prompt_text = prompt_data.get("optimized_prompt", "")
        current_length = len(prompt_text)
        
        if current_length > max_length:
            # Truncate while preserving key elements
            truncated_prompt = self._intelligent_truncate(prompt_text, max_length, prompt_data)
            prompt_data["optimized_prompt"] = truncated_prompt
            prompt_data["character_count"] = len(truncated_prompt)
            prompt_data["within_limits"] = True
            prompt_data["optimization_notes"] += " (Truncated to fit character limit)"
            
        elif current_length < min_length:
            # Expand with additional details
            expanded_prompt = self._intelligent_expand(prompt_text, min_length, prompt_data)
            prompt_data["optimized_prompt"] = expanded_prompt
            prompt_data["character_count"] = len(expanded_prompt)
            prompt_data["within_limits"] = True
            prompt_data["optimization_notes"] += " (Expanded to meet minimum length)"
            
        else:
            prompt_data["character_count"] = current_length
            prompt_data["within_limits"] = True
        
        validated_prompts.append(prompt_data)
    
    return validated_prompts

def _intelligent_truncate(self, prompt: str, max_length: int, prompt_data: Dict) -> str:
    """Intelligently truncate prompt while preserving essential elements."""
    
    if len(prompt) <= max_length:
        return prompt
    
    # Preserve key elements in order of importance
    key_elements = prompt_data.get("key_elements_preserved", [])
    
    # Split into sentences and prioritize
    sentences = prompt.split('. ')
    essential_sentences = []
    optional_sentences = []
    
    for sentence in sentences:
        is_essential = any(element.replace("_", " ") in sentence.lower() for element in key_elements[:3])
        if is_essential:
            essential_sentences.append(sentence)
        else:
            optional_sentences.append(sentence)
    
    # Build truncated prompt
    truncated = '. '.join(essential_sentences)
    
    # Add optional sentences if space allows
    for sentence in optional_sentences:
        test_prompt = truncated + '. ' + sentence
        if len(test_prompt) <= max_length - 10:  # Leave buffer
            truncated = test_prompt
        else:
            break
    
    # Ensure proper ending
    if not truncated.endswith('.'):
        truncated += '.'
    
    return truncated[:max_length]

def _intelligent_expand(self, prompt: str, min_length: int, prompt_data: Dict) -> str:
    """Intelligently expand prompt to meet minimum length requirement."""
    
    if len(prompt) >= min_length:
        return prompt
    
    # Add technical details and atmospheric elements
    expansion_elements = [
        "Professional cinematography with careful attention to composition and framing.",
        "Rich visual textures and detailed environmental elements enhance the scene.",
        "Lighting carefully designed to support the narrative mood and character emotions.",
        "Camera movement and positioning optimized for visual storytelling impact."
    ]
    
    expanded = prompt
    
    for element in expansion_elements:
        test_prompt = expanded + ' ' + element
        if len(test_prompt) <= min_length + 50:  # Allow slight overage
            expanded = test_prompt
        if len(expanded) >= min_length:
            break
    
    return expanded
```

## Implementation Details

### Complete Component Code Structure
```python
from langflow.custom import Component
from langflow.io import DataInput, Output
from langflow.inputs import MessageInput, IntInput, DropdownInput
from langflow.schema import Data
import json
from typing import Dict, List

class StoryboardOptimizer(Component):
    display_name = "010 Storyboard Optimizer"
    description = "Generate final optimized storyboard prompts with user-defined character limits"
    
    inputs = [
        DataInput(name="consistency_report", display_name="Consistency Report"),
        IntInput(name="max_prompt_length", display_name="Maximum Prompt Length", value=500),
        IntInput(name="min_prompt_length", display_name="Minimum Prompt Length", value=200),
        DropdownInput(name="optimization_focus", display_name="Optimization Focus", 
                     options=["visual_detail", "narrative_flow", "technical_precision", "artistic_style", "balanced"], 
                     value="balanced"),
        MessageInput(name="llm", display_name="Language Model")
    ]
    
    outputs = [
        Output(display_name="Optimized Storyboard", name="optimized_storyboard", method="optimize_storyboard")
    ]
    
    def optimize_storyboard(self) -> Data:
        """Main optimization method with user-defined character limits."""
        try:
            # Step 1: Validate user settings
            settings = self.validate_user_settings()
            
            # Step 2: Extract validated scenes
            consistency_data = self.consistency_report.data
            validated_scenes = self.extract_validated_scenes(consistency_data)
            
            if not validated_scenes:
                return Data(data=self._create_error_response("No validated scenes found"))
            
            # Step 3: Optimize scenes into prompts
            optimized_prompts = self.optimize_scene_prompts(validated_scenes, settings)
            
            # Step 4: Validate character limits
            final_prompts = self.validate_prompt_lengths(optimized_prompts, settings)
            
            # Step 5: Generate metadata and reports
            metadata = self._generate_storyboard_metadata(final_prompts, settings)
            optimization_report = self._generate_optimization_report(final_prompts, validated_scenes)
            
            result = {
                "optimized_storyboard": {
                    "storyboard_metadata": metadata,
                    "storyboard_prompts": final_prompts
                },
                "optimization_report": optimization_report
            }
            
            return Data(data=result)
            
        except Exception as e:
            return Data(data=self._create_error_response(f"Storyboard optimization failed: {str(e)}"))
```

## Langflow Integration

### Connection Instructions
**Inputs:**
- Consistency Manager → `consistency_report`
- User Input → `max_prompt_length` (configurable)
- User Input → `min_prompt_length` (configurable)
- User Input → `optimization_focus` (dropdown)
- Language Model (Gemini) → `llm`

**Output:** `optimized_storyboard` → Final Output

### Benefits of This Approach
- ✅ **User-defined character limits** - Complete user control over prompt length
- ✅ **Intelligent optimization** - Preserves essential elements while meeting constraints
- ✅ **Multiple optimization focuses** - Customizable based on user needs
- ✅ **Professional output** - Ready for video generation platforms
- ✅ **Complete workflow** - Final step in comprehensive storyboard generation
- ✅ **No limitations** - Processes complete movies with user-defined constraints

## Next Steps
1. Connect consistency report and configure user settings
2. Test optimization with your narrative and character limits
3. Verify prompt quality and character limit compliance
4. Generate final storyboard prompts for video generation
5. Complete workflow ready for production use
