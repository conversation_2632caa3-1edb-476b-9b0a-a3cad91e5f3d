---
type: "agent_requested"
description: "Example description"
---
# Context Window Limit Handler

When you detect that the context window is approaching its limit (typically when responses are being truncated, you mention running out of space, or the conversation is very long), immediately follow these steps:

1. **STOP** the current response
2. **CREATE** a summary file at the root level named `context-window-summary.md`
3. **INCLUDE** the following structure in the summary:

---
🚨 Context Window Status Check
I'm approaching the context window limit and need to summarize the remaining work for you to continue with a new agent. Here's what I've completed and what still needs to be done:

✅ COMPLETED:
[List all completed tasks, fixes, and created files]

🔄 REMAINING WORK FOR NEW AGENT:
[List all remaining tasks with specific details]

📋 Instructions for New Agent:
[Provide clear continuation instructions]
---

4. **END** your response with: "⚠️ Context window limit reached. Full status saved to `context-window-summary.md` in the root directory."
