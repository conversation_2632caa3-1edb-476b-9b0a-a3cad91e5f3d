# Implementation Guide - Separated Extraction Architecture

## Overview
This guide implements the new **separated extraction architecture** with 10 specialized components for professional storyboard generation.

## Architecture Summary
1. **001-screenplay-parser** - Pure Python screenplay parsing
2. **002-character-extractor** - Character extraction with minimal LLM
3. **003-object-extractor** - Object extraction with minimal LLM
4. **004-environment-extractor** - Environment extraction with minimal LLM
5. **005-other-elements-extractor** - Other elements with minimal LLM
6. **006-description-enhancer** - Creative LLM enhancement
7. **007-shot-generator** - Director of Photography LLM
8. **008-scene-descriptor** - Pure Python data combination
9. **009-consistency-manager** - Pure Python consistency validation
10. **010-storyboard-optimizer** - Final LLM optimization with user-defined character limits

## Step-by-Step Implementation Plan

### Phase 1: Foundation Components (Pure Python)

#### 1.1 Screenplay Parser
**File:** `001-screenplay-parser.md` ✅ **COMPLETED**
**Type:** Pure Python (no LLM calls)
**Purpose:** Parse screenplay text into structured data

**Implementation priority:** START HERE
- No LLM dependencies = no token issues
- Tests your Python component setup
- Provides clean data for all subsequent components

#### 1.2 Scene Descriptor
**File:** `008-scene-descriptor.md` ✅ **COMPLETED**
**Type:** Pure Python (data combination)
**Purpose:** Combine all extraction outputs into complete scenes

**Implementation priority:** IMPLEMENT AFTER EXTRACTORS
- Pure Python efficiency for data processing
- No LLM overhead for structural operations
- Comprehensive scene assembly

#### 1.3 Consistency Manager
**File:** `009-consistency-manager.md` ✅ **COMPLETED**
**Type:** Pure Python (validation)
**Purpose:** Ensure visual and narrative consistency

**Implementation priority:** IMPLEMENT AFTER SCENE DESCRIPTOR
- Automated consistency checking
- Quality assurance for professional output
- No LLM costs for validation

### Phase 2: Extraction Components (Minimal LLM Usage)

#### 2.1 Character Extractor
**File:** `002-character-extractor.md` ✅ **COMPLETED**
**Type:** Mostly Python + Minimal LLM
**LLM Usage:** Only for ambiguous character descriptions

**Key features:**
- Text-based extraction handles 80%+ of cases
- LLM only called for unclear descriptions
- Efficient token usage

#### 2.2 Object Extractor
**File:** `003-object-extractor.md` ✅ **COMPLETED**
**Type:** Mostly Python + Minimal LLM
**LLM Usage:** Only for ambiguous object descriptions

#### 2.3 Environment Extractor
**File:** `004-environment-extractor.md` ✅ **COMPLETED**
**Type:** Mostly Python + Minimal LLM
**LLM Usage:** Only for ambiguous environment descriptions

#### 2.4 Other Elements Extractor
**File:** `005-other-elements-extractor.md` ✅ **COMPLETED**
**Type:** Mostly Python + Minimal LLM
**LLM Usage:** Only for abstract elements (emotions, atmosphere, effects)

### Phase 3: Creative Enhancement (LLM Components)

#### 3.1 Description Enhancer
**File:** `006-description-enhancer.md` ✅ **COMPLETED**
**Type:** LLM Component (Creative Enhancement)
**LLM Usage:** Enhances all extracted elements with creative visual descriptions

**Key features:**
- Transforms basic extracted data into rich visual descriptions
- Processes all element types for artistic enhancement
- Efficient batching for LLM optimization

#### 3.2 Shot Generator (Director of Photography)
**File:** `007-shot-generator.md` ✅ **COMPLETED**
**Type:** LLM Component (Cinematography)
**LLM Usage:** Professional cinematography and shot composition

**Key features:**
- Acts as Director of Photography
- Creates detailed camera angles, movements, compositions
- Professional technical specifications for storyboard artists

### Phase 4: Final Assembly and Optimization

#### 4.1 Storyboard Optimizer
**File:** `010-storyboard-optimizer.md` ✅ **COMPLETED**
**Type:** LLM Component (Final Optimization)
**LLM Usage:** Final prompt optimization with user-defined character limits

**Key features:**
- **User-defined character limits** - Complete user control
- Multiple optimization focuses (visual_detail, narrative_flow, technical_precision, artistic_style, balanced)
- Intelligent truncation and expansion to meet constraints
- Ready for video generation platforms

## Implementation Order

### Week 1: Foundation (Pure Python)
1. ✅ **001-screenplay-parser** - Parse screenplay structure
2. ✅ **008-scene-descriptor** - Combine all data
3. ✅ **009-consistency-manager** - Validate consistency

### Week 2: Extraction Phase (Minimal LLM)
1. ✅ **002-character-extractor** - Extract characters
2. ✅ **003-object-extractor** - Extract objects
3. ✅ **004-environment-extractor** - Extract environments
4. ✅ **005-other-elements-extractor** - Extract other elements

### Week 3: Creative Enhancement (LLM)
1. ✅ **006-description-enhancer** - Creative visual enhancement
2. ✅ **007-shot-generator** - Director of Photography

### Week 4: Final Optimization (LLM)
1. ✅ **010-storyboard-optimizer** - Final prompt optimization

## Langflow Connection Instructions

### New Separated Architecture Setup:
```
File → 001-Screenplay-Parser →
├── 002-Character-Extractor → Language Model (Gemini)
├── 003-Object-Extractor → Language Model (Gemini)
├── 004-Environment-Extractor → Language Model (Gemini)
└── 005-Other-Elements-Extractor → Language Model (Gemini)
                ↓
006-Description-Enhancer → Language Model (Gemini)
                ↓
007-Shot-Generator → Language Model (Gemini)
                ↓
008-Scene-Descriptor (Pure Python)
                ↓
009-Consistency-Manager (Pure Python)
                ↓
010-Storyboard-Optimizer → Language Model (Gemini)
```

## Key Implementation Features

### User-Configurable Settings
- **Character limits:** User-defined min/max prompt lengths
- **Optimization focus:** Visual detail, narrative flow, technical precision, artistic style, or balanced
- **No quality settings:** All components use best quality (as requested)

### LLM Usage Strategy
- **Minimal LLM components:** Python handles 80%+ of extraction, LLM only for ambiguous cases
- **Creative LLM components:** Full LLM usage for artistic and cinematographic tasks
- **Pure Python components:** Data processing, validation, and assembly

### Industry Standards
- **snake_case identifiers** throughout all components
- **No artificial limitations** - processes complete movies
- **Professional output** ready for storyboard generation

### Error Handling
- **Robust validation** at each component
- **Graceful fallbacks** when LLM calls fail
- **Comprehensive error reporting**

## Testing Strategy

### 1. Component-by-Component Testing
Test each component individually before connecting:
```bash
# Test screenplay parser
python test_screenplay_parser.py

# Test subject extractor with sample data
python test_subject_extractor.py

# etc.
```

### 2. Integration Testing
Connect components one by one:
1. File → Screenplay Parser
2. Add Subject Extractor
3. Add Description Enhancer
4. Continue incrementally

### 3. Token Usage Monitoring
Track token usage at each LLM call:
- Old workflow: ~5000+ tokens across 5 calls
- New workflow: ~2000-3000 tokens across 3-4 calls

### 4. JSON Validation Testing
Test with intentionally incomplete responses:
- Simulate token limit cutoffs
- Verify auto-repair functionality
- Test retry mechanisms

## Error Handling Strategy

### 1. JSON Validation Errors
```python
# If JSON is incomplete:
if not JSONValidator.is_valid_json(response):
    # Try to repair
    repaired = JSONValidator.repair_incomplete_json(response)
    if repaired:
        return repaired
    else:
        # Retry with simplified prompt
        return retry_with_fallback()
```

### 2. LLM API Errors
```python
# If API call fails:
try:
    response = llm.call(prompt)
except Exception as e:
    # Log error and retry
    logger.error(f"LLM call failed: {e}")
    return retry_with_exponential_backoff()
```

### 3. Token Limit Errors
```python
# If prompt too long:
if len(prompt) > token_limit:
    # Split into chunks or simplify
    simplified_prompt = create_simplified_prompt(data)
    return llm.call(simplified_prompt)
```

## Success Metrics

- ✅ **All 10 components implemented** with proper specifications
- ✅ **User-defined character limits** working correctly
- ✅ **No quality settings** - all components use best quality
- ✅ **Complete movie processing** without limitations
- ✅ **Professional storyboard output** ready for video generation
- ✅ **Efficient LLM usage** with minimal token consumption
- ✅ **Industry standard identifiers** (snake_case throughout)

## Next Steps

1. **Implement components in order** (Foundation → Extraction → Enhancement → Optimization)
2. **Test each phase** before proceeding to next
3. **Configure user settings** for character limits and optimization focus
4. **Validate final output** meets video generation requirements
5. **Document any customizations** needed for your specific use case
