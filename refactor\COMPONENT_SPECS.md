# Component Specifications - Separated Extraction Architecture

## Component Architecture Overview

Each component follows this pattern:
- **Input validation** - Ensure data integrity
- **Processing logic** - Core functionality (Python or Python+LLM)
- **Output validation** - Ensure complete, valid responses
- **Error handling** - Graceful failure and recovery
- **Clear LLM usage** - Document when and why LLMs are used
- **No artificial limitations** - Support complete movie processing
- **Industry standards** - snake_case identifiers, Langflow best practices

## 001-screenplay-parser.py

### Purpose
Parse screenplay/narrative text into structured data for storyboard generation workflow.

### Type
**Pure Python** - No LLM calls needed

### LLM Usage
**None** - This component uses only Python text processing

### Inputs
- `narrative_text` (string) - Raw screenplay or narrative text

### Outputs
```json
{
  "scenes": [
    {
      "scene_number": 1,
      "location": "string",
      "time": "string",
      "description": "string",
      "action_blocks": ["string"]
    }
  ],
  "characters": [
    {
      "name": "string",
      "mentions": ["string"]
    }
  ],
  "metadata": {
    "total_scenes": 0,
    "total_characters": 0,
    "word_count": 0
  }
}
```

### Key Features
- **Complete movie processing** - No scene limitations
- **Industry standard identifiers** - snake_case format
- **Regex-based parsing** for screenplay format
- **Character name extraction** with mentions
- **Scene boundary detection** without artificial limits
- **Action block segmentation** for complete narrative

### Langflow Connection
**Input:** File component → `narrative_text`
**Output:** `parsed_data` → Character Extractor, Object Extractor, Environment Extractor, Other Elements Extractor

---

## 002-character-extractor.py

### Purpose
Extract characters with visual descriptions for storyboard generation.

### Type
**Mostly Python** - Minimal LLM usage for ambiguous cases only

### LLM Usage
**When used:** Only for characters with insufficient visual description from text
**What LLM does:** Provides creative visual details for ambiguous character descriptions
**Why needed:** Some characters mentioned without visual details require creative interpretation
**Token usage:** Minimal - only called for unclear cases, not every character

### Inputs
- `parsed_screenplay` (data) - From screenplay parser
- `llm` (Language Model) - Gemini 2.5 connection (used minimally)

### Outputs
```json
{
  "extracted_characters": [
    {
      "character_id": "little_red_riding_hood",
      "character_name": "Little Red Riding Hood",
      "visual_description": "young girl with red hooded cloak, carrying wicker basket",
      "extraction_method": "text_analysis",
      "confidence": 0.95,
      "text_references": ["string"],
      "scene_appearances": [1, 2, 3],
      "character_traits": ["innocent", "curious"],
      "relationships": [
        {
          "related_character": "grandmother",
          "relationship_type": "family",
          "description": "granddaughter visiting grandmother"
        }
      ]
    }
  ],
  "extraction_summary": {
    "total_characters": 5,
    "text_extracted": 4,
    "llm_enhanced": 1,
    "processing_notes": "Character extraction completed successfully"
  }
}
```

### Key Features
- **Focused extraction** - Only handles characters
- **Text-based primary extraction** - Python analysis first
- **Minimal LLM usage** - Only for ambiguous cases
- **Relationship detection** - Character connections
- **snake_case identifiers** - Industry standard
- **Complete movie support** - No character limitations

### Langflow Connection
**Inputs:**
- Screenplay Parser → `parsed_screenplay`
- Language Model (Gemini) → `llm`
**Output:** `extracted_characters` → Description Enhancer

---

## 003-object-extractor.py

### Purpose
Extract objects and props with visual descriptions for storyboard generation.

### Type
**Mostly Python** - Minimal LLM usage for ambiguous cases only

### LLM Usage
**When used:** Only for objects with insufficient visual description from text
**What LLM does:** Provides creative visual details for ambiguous object descriptions
**Why needed:** Some objects mentioned without visual details require creative interpretation
**Token usage:** Minimal - only called for unclear cases, not every object

### Inputs
- `parsed_screenplay` (data) - From screenplay parser
- `llm` (Language Model) - Gemini 2.5 connection (used minimally)

### Outputs
```json
{
  "extracted_objects": [
    {
      "object_id": "wicker_basket",
      "object_name": "Wicker Basket",
      "object_category": "prop",
      "visual_description": "brown wicker basket with red checkered cloth",
      "extraction_method": "text_analysis",
      "confidence": 0.95,
      "text_references": ["string"],
      "scene_appearances": [1, 2],
      "object_properties": ["portable", "container"],
      "related_characters": ["little_red_riding_hood"],
      "story_importance": "high"
    }
  ],
  "extraction_summary": {
    "total_objects": 12,
    "text_extracted": 10,
    "llm_enhanced": 2,
    "categories": {
      "props": 5,
      "furniture": 4,
      "clothing": 2,
      "food": 1
    }
  }
}
```

### Key Features
- **Focused extraction** - Only handles objects and props
- **Category classification** - Props, furniture, clothing, food, etc.
- **Story importance assessment** - High/medium/low priority
- **Object-character relationships** - Who uses what
- **snake_case identifiers** - Industry standard
- **Complete movie support** - No object limitations

### Langflow Connection
**Inputs:**
- Screenplay Parser → `parsed_screenplay`
- Language Model (Gemini) → `llm`
**Output:** `extracted_objects` → Description Enhancer

---

## 002-subject-extractor.py

### Purpose
Extract all important subjects (characters, objects, environments) with detailed visual descriptions.

### Type
**Hybrid** - Python validation + 1 LLM call

### Inputs
- `parsed_screenplay` (data) - From screenplay parser
- `llm` (Language Model) - Gemini 2.5 connection

### Outputs
```json
{
  "subjects": [
    {
      "identifier": "string",
      "exact_phrase": "string",
      "category": "character|object|environment|other",
      "relationships": [
        {
          "related_subject": "string",
          "relationship_type": "string",
          "description": "string"
        }
      ],
      "text_references": ["string"],
      "confidence": 0.95
    }
  ],
  "analysis": {
    "subject_count": 0,
    "ambiguities": [
      {
        "subject": "string",
        "issue": "string"
      }
    ],
    "processing_notes": "string"
  }
}
```

### Key Features
- **JSON validation with auto-repair** - Fixes your "possession"... issue
- **Retry logic** - If response incomplete, retry with simplified prompt
- **Confidence scoring** - Rate extraction quality
- **Relationship mapping** - Track subject interactions

### LLM Prompt Strategy
```python
system_prompt = """You are an expert at extracting subjects from narrative text.
Focus on visual elements that can be consistently described across multiple images.
Respond with valid JSON only."""

user_prompt = f"""Extract subjects from: {narrative_text}
Required format: {json_schema}
Keep descriptions detailed but concise to avoid token limits."""
```

### Error Handling
1. **Incomplete JSON**: Auto-repair with closing brackets
2. **Token limits**: Retry with shorter prompt
3. **Missing fields**: Request specific missing data
4. **API errors**: Exponential backoff retry

### Langflow Connection
**Inputs:** 
- Screenplay Parser → `parsed_screenplay`
- Language Model (Gemini) → `llm`
**Output:** `extracted_subjects` → Description Enhancer

---

## 003-description-enhancer.py

### Purpose
Enhance incomplete visual descriptions while maintaining consistency.

### Type
**Hybrid** - Python logic + 1 LLM call

### Inputs
- `extracted_subjects` (data) - From subject extractor
- `llm` (Language Model) - Gemini 2.5 connection

### Outputs
```json
{
  "enhanced_subjects": [
    {
      "identifier": "string",
      "original_phrase": "string",
      "enhanced_phrase": "string",
      "category": "string",
      "enhancement_notes": "string",
      "enhancement_type": "visual|contextual|consistency",
      "relationships": [],
      "ambiguities_resolved": [
        {
          "original_issue": "string",
          "resolution": "string"
        }
      ]
    }
  ],
  "enhancement_summary": {
    "subjects_enhanced": 0,
    "subjects_unchanged": 0,
    "enhancement_approach": "string",
    "total_ambiguities_resolved": 0
  }
}
```

### Key Features
- **Progressive enhancement** - Start basic, add details gradually
- **Consistency checking** - Ensure descriptions don't conflict
- **Smart chunking** - Process subjects in groups to manage tokens
- **Enhancement tracking** - Record what was changed and why

### Enhancement Strategy
1. **Identify incomplete descriptions** - Missing visual details
2. **Contextual enhancement** - Add period-appropriate details
3. **Visual consistency** - Ensure descriptions work for image generation
4. **Relationship preservation** - Maintain subject connections

### Langflow Connection
**Inputs:**
- Subject Extractor → `extracted_subjects`
- Language Model (Gemini) → `llm`
**Output:** `enhanced_subjects` → Shot Generator

---

## 004-shot-generator.py

### Purpose
Generate cinematic shots with professional camera work and timing.

### Type
**Hybrid** - Python analysis + 1 LLM call (AI creativity needed)

### Inputs
- `enhanced_subjects` (data) - From description enhancer
- `parsed_screenplay` (data) - Scene context
- `llm` (Language Model) - Gemini 2.5 connection
- `genre` (string) - Film genre for shot style
- `shot_duration` (string) - Default shot length

### Outputs
```json
{
  "shots": [
    {
      "shot_number": 1,
      "duration": "8 seconds",
      "scene_context": "string",
      "description": "string",
      "camera_work": {
        "angle": "close-up|medium|wide|extreme-wide",
        "movement": "static|pan|tilt|dolly|zoom",
        "framing": "string",
        "focus": "string"
      },
      "characters_present": ["string"],
      "visual_elements": ["string"],
      "mood": "string",
      "technical_notes": "string"
    }
  ],
  "shot_summary": {
    "total_shots": 0,
    "average_duration": "string",
    "genre_style": "string",
    "coverage_analysis": "string"
  }
}
```

### Key Features
- **Genre-aware shot selection** - Different styles for drama vs action
- **Professional cinematography** - Industry-standard camera work
- **Narrative flow** - Shots that tell the story effectively
- **Technical specifications** - Camera angles, movements, framing

### Langflow Connection
**Inputs:**
- Description Enhancer → `enhanced_subjects`
- Screenplay Parser → `parsed_screenplay`
- Language Model (Gemini) → `llm`
**Output:** `generated_shots` → Scene Descriptor

---

## 005-scene-descriptor.py

### Purpose
Create detailed scene descriptions for text-to-image generation.

### Type
**Mostly Python** - Data combination with minimal LLM use

### Inputs
- `generated_shots` (data) - From shot generator
- `enhanced_subjects` (data) - Subject descriptions

### Outputs
```json
{
  "scene_descriptions": [
    {
      "shot_number": 1,
      "scene_description": "string",
      "included_subjects": [
        {
          "identifier": "string",
          "role_in_scene": "string",
          "exact_phrase": "string"
        }
      ],
      "scene_elements": {
        "time_of_day": "string",
        "lighting": "string",
        "weather": "string",
        "mood": "string",
        "perspective": "string",
        "composition": "string"
      },
      "technical_specs": {
        "camera_angle": "string",
        "focal_length": "string",
        "depth_of_field": "string"
      }
    }
  ]
}
```

### Key Features
- **Data synthesis** - Combine shots and subjects intelligently
- **Visual coherence** - Ensure scenes make visual sense
- **Technical accuracy** - Proper camera and lighting terms
- **Consistency checking** - Verify subject descriptions match

### Langflow Connection
**Inputs:**
- Shot Generator → `generated_shots`
- Description Enhancer → `enhanced_subjects`
**Output:** `scene_descriptions` → Consistency Manager

---

## 006-consistency-manager.py

### Purpose
Ensure subject consistency across multiple prompts and scenes.

### Type
**Pure Python** - Data processing and validation

### Inputs
- `scene_descriptions` (data) - From scene descriptor
- `enhanced_subjects` (data) - Master subject list

### Outputs
```json
{
  "consistent_prompts": [
    {
      "shot_number": 1,
      "image_prompt": "string",
      "subjects_used": [
        {
          "identifier": "string",
          "exact_phrase": "string",
          "consistency_notes": "string"
        }
      ],
      "consistency_score": 0.95,
      "validation_notes": "string"
    }
  ],
  "consistency_report": {
    "total_prompts": 0,
    "average_consistency_score": 0.95,
    "subjects_tracked": 0,
    "consistency_issues": []
  }
}
```

### Key Features
- **Exact phrase matching** - Ensure identical subject descriptions
- **Relationship tracking** - Maintain subject interactions
- **Consistency scoring** - Rate prompt consistency
- **Issue detection** - Flag potential problems

### Langflow Connection
**Input:** Scene Descriptor → `scene_descriptions`
**Output:** `consistent_prompts` → VEO3 Optimizer

---

## 007-veo3-optimizer.py

### Purpose
Optimize prompts specifically for VEO3 video generation platform.

### Type
**Hybrid** - Python optimization + 1 LLM call for final polish

### Inputs
- `consistent_prompts` (data) - From consistency manager
- `llm` (Language Model) - Gemini 2.5 connection
- `max_characters` (int) - Character limit for VEO3
- `aspect_ratio` (string) - Video aspect ratio
- `duration` (string) - Video duration

### Outputs
```json
{
  "veo3_prompts": [
    {
      "shot_number": 1,
      "optimized_prompt": "string",
      "character_count": "user_defined_max",
      "optimization_notes": "string",
      "technical_parameters": {
        "aspect_ratio": "16:9",
        "duration": "8 seconds"
      },
      "veo3_specific_tags": ["string"]
    }
  ],
  "optimization_summary": {
    "total_prompts": 0,
    "average_character_count": "calculated_from_user_max",
    "optimization_approach": "string",
    "veo3_compatibility": "high"
  }
}
```

### Key Features
- **Character limit optimization** - Stay within VEO3 limits
- **Platform-specific formatting** - VEO3 best practices
- **Technical parameter integration** - Aspect ratio, duration
- **User-defined character limits** - Flexible prompt length control

### Langflow Connection
**Inputs:**
- Consistency Manager → `consistent_prompts`
- Language Model (Gemini) → `llm`
**Output:** `veo3_prompts` (Final output)
