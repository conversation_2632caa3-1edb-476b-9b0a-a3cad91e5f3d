---
type: "always_apply"
---

# Summary Output Handler

When generating any comprehensive summary, analysis, or detailed report:

1. **NEVER** output the full summary in the chat window
2. **CREATE** a summary file at the root level named `summary-[timestamp].md` or `summary-[topic].md`
3. **INCLUDE** the full detailed content in the .md file
4. **RESPOND** in chat with only:
   - Brief overview (2-3 sentences max)
   - File location
   - Next steps if applicable

Example response format:
```
✅ Summary completed covering [brief description].
📄 Full details saved to: `summary-project-analysis.md`
🔄 Next: [any immediate next steps]
```
