# 005 Other Elements Extractor Component

## Overview
**Type:** Mostly Python Component (Minimal LLM usage for ambiguous cases only)
**Purpose:** Extract additional visual elements (effects, sounds, actions, emotions) for storyboard generation
**Priority:** HIGH - Part of the separated extraction architecture

## LLM Usage Documentation
**When LLM is used:** Only for ambiguous visual elements that cannot be determined from text analysis
**What LLM does:** Provides creative visual interpretation for abstract elements (emotions, effects, atmosphere)
**Why LLM is needed:** Abstract elements like emotions, magical effects, or atmospheric details require creative interpretation
**Token usage:** Minimal - only called for unclear cases, not for every element

## Input Specification
```python
inputs = [
    DataInput(
        name="parsed_screenplay", 
        display_name="Parsed Screenplay",
        info="Structured screenplay data from parser"
    ),
    MessageInput(
        name="llm", 
        display_name="Language Model",
        info="Connect Gemini 2.5 (used only for ambiguous cases)"
    )
]
```

## Output Specification
```json
{
  "extracted_other_elements": [
    {
      "element_id": "mysterious_atmosphere",
      "element_name": "Mysterious Atmosphere",
      "element_category": "atmosphere",
      "element_type": "visual_effect",
      "visual_description": "subtle shadows, dim lighting creating sense of unease and mystery",
      "extraction_method": "text_analysis",
      "confidence": 0.85,
      "text_references": [
        "Something seems different about the figure in the bed",
        "She approaches cautiously"
      ],
      "scene_appearances": [3],
      "element_properties": ["subtle", "psychological", "atmospheric"],
      "visual_impact": "medium",
      "story_importance": "high",
      "implementation_notes": "Use lighting and shadow to create mood"
    },
    {
      "element_id": "surprise_emotion",
      "element_name": "Surprise Emotion",
      "element_category": "emotion",
      "element_type": "character_expression",
      "visual_description": "wide eyes, open mouth, body language showing shock and realization",
      "extraction_method": "text_analysis",
      "confidence": 0.90,
      "text_references": [
        "Grandmother, what big eyes you have!",
        "All the better to see you with, my dear"
      ],
      "scene_appearances": [3],
      "element_properties": ["facial_expression", "body_language", "emotional"],
      "visual_impact": "high",
      "story_importance": "high",
      "implementation_notes": "Focus on character facial expressions and body language"
    },
    {
      "element_id": "door_creaking_sound",
      "element_name": "Door Creaking Sound",
      "element_category": "sound_effect",
      "element_type": "audio_visual",
      "visual_description": "visual representation of sound through motion lines, dust particles",
      "extraction_method": "text_analysis",
      "confidence": 0.75,
      "text_references": [
        "The door creaks open"
      ],
      "scene_appearances": [1],
      "element_properties": ["motion", "atmospheric", "transitional"],
      "visual_impact": "low",
      "story_importance": "medium",
      "implementation_notes": "Show door movement with visual sound indicators"
    }
  ],
  "extraction_summary": {
    "total_elements": 15,
    "text_extracted": 12,
    "llm_enhanced": 3,
    "element_categories": {
      "atmosphere": 4,
      "emotion": 5,
      "sound_effect": 3,
      "visual_effect": 2,
      "action": 1
    },
    "element_types": {
      "visual_effect": 6,
      "character_expression": 5,
      "audio_visual": 3,
      "motion": 1
    },
    "ambiguous_cases": [
      {
        "element": "magical_transformation",
        "issue": "Abstract concept requiring visual interpretation",
        "resolution": "LLM provided creative visual representation"
      }
    ],
    "processing_notes": "Other elements extraction completed successfully"
  }
}
```

## Core Functionality

### 1. Text-Based Element Extraction (Primary Method)
```python
def extract_other_elements_from_text(self, screenplay_data: Dict) -> List[Dict]:
    """Extract other visual elements using Python text analysis (no LLM needed)."""
    
    elements = {}
    scenes = screenplay_data.get("scenes", [])
    
    # Element detection patterns
    element_patterns = {
        "emotion": [
            r'\b(surprised?|shocked?|afraid|scared|happy|sad|angry|confused|curious)\b',
            r'\b(smiling|frowning|crying|laughing|gasping|screaming)\b',
            r'\b(cautiously|nervously|confidently|hesitantly)\b'
        ],
        "atmosphere": [
            r'\b(mysterious|eerie|peaceful|tense|calm|chaotic|serene|ominous)\b',
            r'\b(something seems different|strange|unusual|odd)\b',
            r'\b(atmosphere|mood|feeling|sense)\b'
        ],
        "sound_effect": [
            r'\b(creaks?|squeaks?|bangs?|crashes?|whispers?|shouts?)\b',
            r'\b(footsteps|breathing|heartbeat|wind|rustling)\b',
            r'\b(knock|slam|click|thud|splash)\b'
        ],
        "visual_effect": [
            r'\b(shadows?|light|darkness|glow|shimmer|sparkle)\b',
            r'\b(smoke|mist|fog|dust|particles)\b',
            r'\b(reflection|mirror|glass|crystal)\b'
        ],
        "action": [
            r'\b(enters?|exits?|walks?|runs?|jumps?|falls?)\b',
            r'\b(opens?|closes?|picks? up|puts? down|carries?)\b',
            r'\b(approaches?|retreats?|hides?|reveals?)\b'
        ]
    }
    
    element_types = {
        "emotion": "character_expression",
        "atmosphere": "visual_effect",
        "sound_effect": "audio_visual",
        "visual_effect": "visual_effect",
        "action": "motion"
    }
    
    for scene in scenes:
        scene_num = scene["scene_number"]
        action_blocks = scene.get("action_blocks", [])
        
        for block in action_blocks:
            block_lower = block.lower()
            
            # Check each category
            for category, patterns in element_patterns.items():
                for pattern in patterns:
                    matches = re.findall(pattern, block_lower, re.IGNORECASE)
                    
                    for match in matches:
                        element_id = self._create_element_id(match, category)
                        
                        if element_id not in elements:
                            elements[element_id] = {
                                "element_id": element_id,
                                "element_name": match.title().replace("_", " "),
                                "element_category": category,
                                "element_type": element_types[category],
                                "visual_description": self._create_visual_description(match, category, block),
                                "extraction_method": "text_analysis",
                                "confidence": 0.8,
                                "text_references": [],
                                "scene_appearances": [],
                                "element_properties": self._extract_element_properties(match, category),
                                "visual_impact": self._assess_visual_impact(category),
                                "story_importance": "medium",
                                "implementation_notes": self._create_implementation_notes(match, category)
                            }
                        
                        # Add reference and scene
                        elements[element_id]["text_references"].append(block)
                        if scene_num not in elements[element_id]["scene_appearances"]:
                            elements[element_id]["scene_appearances"].append(scene_num)
    
    return list(elements.values())

def _create_element_id(self, match: str, category: str) -> str:
    """Create snake_case identifier from element."""
    base_name = match.lower().replace(" ", "_").replace("'", "").replace("-", "_")
    return f"{base_name}_{category}"

def _create_visual_description(self, match: str, category: str, context: str) -> str:
    """Create basic visual description from text analysis."""
    
    descriptions = {
        "emotion": {
            "surprised": "wide eyes, open mouth, body language showing shock",
            "scared": "fearful expression, tense posture, defensive stance",
            "cautiously": "careful movements, alert expression, hesitant body language",
            "happy": "smiling expression, relaxed posture, bright eyes"
        },
        "atmosphere": {
            "mysterious": "subtle shadows, dim lighting creating sense of mystery",
            "peaceful": "soft lighting, calm colors, serene composition",
            "tense": "sharp contrasts, dramatic lighting, angular compositions"
        },
        "sound_effect": {
            "creaks": "visual representation of sound through motion lines",
            "footsteps": "dust particles, floor movement indicators",
            "knock": "impact lines, door vibration effects"
        },
        "visual_effect": {
            "shadows": "dark areas with defined edges and gradients",
            "light": "bright illumination with rays and highlights",
            "glow": "soft radiant light emanating from source"
        },
        "action": {
            "enters": "character movement through doorway or entrance",
            "approaches": "forward movement with purposeful direction",
            "carries": "character holding object with appropriate posture"
        }
    }
    
    if category in descriptions and match.lower() in descriptions[category]:
        return descriptions[category][match.lower()]
    
    return f"visual representation of {match} in {category} context"

def _extract_element_properties(self, match: str, category: str) -> List[str]:
    """Extract properties based on element type."""
    
    property_map = {
        "emotion": ["facial_expression", "body_language", "emotional"],
        "atmosphere": ["atmospheric", "mood", "environmental"],
        "sound_effect": ["motion", "atmospheric", "transitional"],
        "visual_effect": ["lighting", "atmospheric", "visual"],
        "action": ["motion", "character", "narrative"]
    }
    
    base_properties = property_map.get(category, ["general"])
    
    # Add specific properties based on match
    if "subtle" in match.lower() or "gentle" in match.lower():
        base_properties.append("subtle")
    if "dramatic" in match.lower() or "intense" in match.lower():
        base_properties.append("dramatic")
    
    return base_properties

def _assess_visual_impact(self, category: str) -> str:
    """Assess the visual impact level of the element."""
    
    impact_levels = {
        "emotion": "high",
        "atmosphere": "medium", 
        "sound_effect": "low",
        "visual_effect": "medium",
        "action": "high"
    }
    
    return impact_levels.get(category, "medium")

def _create_implementation_notes(self, match: str, category: str) -> str:
    """Create implementation notes for storyboard artists."""
    
    notes = {
        "emotion": f"Focus on character facial expressions and body language to convey {match}",
        "atmosphere": f"Use lighting, color, and composition to create {match} mood",
        "sound_effect": f"Show {match} through visual indicators like motion lines or particles",
        "visual_effect": f"Implement {match} using appropriate lighting and visual techniques",
        "action": f"Show {match} through clear character positioning and movement"
    }
    
    return notes.get(category, f"Implement {match} visually in storyboard")
```

### 2. LLM Enhancement (Only for Ambiguous Cases)
```python
def enhance_ambiguous_elements(self, elements: List[Dict]) -> List[Dict]:
    """Use LLM only for elements with insufficient visual description."""
    
    ambiguous_elements = []
    enhanced_elements = []
    
    for element in elements:
        # Check if element needs enhancement
        if (len(element["visual_description"]) < 25 or 
            element["element_category"] in ["atmosphere", "emotion"] and element["confidence"] < 0.8):
            ambiguous_elements.append(element)
        else:
            enhanced_elements.append(element)
    
    if not ambiguous_elements:
        return elements  # No LLM needed
    
    # Use LLM only for ambiguous cases
    for element in ambiguous_elements:
        enhanced = self._enhance_element_with_llm(element)
        enhanced_elements.append(enhanced)
    
    return enhanced_elements

def _enhance_element_with_llm(self, element: Dict) -> Dict:
    """Use LLM to enhance element with minimal visual description."""
    
    prompt = f"""You are a visual description specialist for storyboard generation.

Element: {element['element_name']}
Category: {element['element_category']}
Type: {element['element_type']}
Current description: {element['visual_description']}
Text references: {element['text_references']}

Provide a concise visual description for storyboard generation. Focus on:
- How to visually represent this element
- Specific visual techniques to use
- Color, lighting, and composition suggestions
- Clear implementation guidance

Respond with JSON:
{{
  "enhanced_visual_description": "detailed visual description here",
  "enhancement_notes": "explanation of additions made"
}}"""

    try:
        response = self.llm.invoke(prompt)
        result = self._parse_llm_response(response)
        
        element["visual_description"] = result.get("enhanced_visual_description", element["visual_description"])
        element["extraction_method"] = "llm_enhanced"
        element["enhancement_notes"] = result.get("enhancement_notes", "")
        
        return element
        
    except Exception as e:
        # Fallback: keep original description
        element["enhancement_notes"] = f"LLM enhancement failed: {str(e)}"
        return element
```

## Implementation Details

### Complete Component Code Structure
```python
from langflow.custom import Component
from langflow.io import DataInput, Output
from langflow.inputs import MessageInput
from langflow.schema import Data
import json
import re
from typing import Dict, List

class OtherElementsExtractor(Component):
    display_name = "005 Other Elements Extractor"
    description = "Extract additional visual elements for storyboard generation"
    
    inputs = [
        DataInput(name="parsed_screenplay", display_name="Parsed Screenplay"),
        MessageInput(name="llm", display_name="Language Model")
    ]
    
    outputs = [
        Output(display_name="Extracted Other Elements", name="extracted_other_elements", method="extract_other_elements")
    ]
    
    def extract_other_elements(self) -> Data:
        """Main extraction method with minimal LLM usage."""
        try:
            screenplay_data = self.parsed_screenplay.data
            
            if not screenplay_data or "scenes" not in screenplay_data:
                return Data(data=self._create_error_response("Invalid screenplay data"))
            
            # Step 1: Extract elements using text analysis (Python only)
            elements = self.extract_other_elements_from_text(screenplay_data)
            
            # Step 2: Assess story importance (Python only)
            elements = self.assess_story_importance(elements)
            
            # Step 3: Enhance only ambiguous cases with LLM (minimal usage)
            elements = self.enhance_ambiguous_elements(elements)
            
            # Step 4: Create summary
            summary = self._create_extraction_summary(elements)
            
            result = {
                "extracted_other_elements": elements,
                "extraction_summary": summary
            }
            
            return Data(data=result)
            
        except Exception as e:
            return Data(data=self._create_error_response(f"Other elements extraction failed: {str(e)}"))
```

## Langflow Integration

### Connection Instructions
**Inputs:**
- Screenplay Parser → `parsed_screenplay`
- Language Model (Gemini) → `llm` (used minimally)

**Output:** `extracted_other_elements` → Description Enhancer

### Benefits of This Approach
- ✅ **Comprehensive extraction** - Captures atmosphere, emotions, effects, sounds, actions
- ✅ **Minimal LLM usage** - Python handles most analysis
- ✅ **Visual implementation guidance** - Clear notes for storyboard artists
- ✅ **Industry standard** - snake_case identifiers
- ✅ **No limitations** - Processes complete movies
- ✅ **Creative enhancement** - LLM used only for abstract elements

## Next Steps
1. Implement in parallel with other extractors
2. Test element extraction with your narrative
3. Verify minimal LLM usage for abstract elements
4. Connect to Description Enhancer (receives all extraction outputs)
5. Continue with creative enhancement phase
