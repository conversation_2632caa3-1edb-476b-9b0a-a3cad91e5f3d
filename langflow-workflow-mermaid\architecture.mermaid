flowchart TD
    %% Title
    title[🎬 Final State<br/>10 Specialized Components for Professional Storyboard Generation]
    
    %% Input
    FILE[📄 File Input<br/>Narrative Text] --> SP

    %% Foundation Layer (Pure Python)
    subgraph FOUNDATION["🏗️ Foundation Layer - Pure Python"]
        SP[001 Screenplay Parser<br/>🐍 Pure Python<br/>Parse screenplay structure]
        SD[008 Scene Descriptor<br/>🐍 Pure Python<br/>Combine all data]
        CM[009 Consistency Manager<br/>🐍 Pure Python<br/>Validate consistency]
    end

    %% Extraction Layer (Minimal LLM)
    subgraph EXTRACTION["🔍 Extraction Layer - Minimal LLM Usage"]
        CE[002 Character Extractor<br/>🐍 Minimal LLM<br/>Extract characters]
        OE[003 Object Extractor<br/>🐍 Minimal LLM<br/>Extract objects]
        EE[004 Environment Extractor<br/>🐍 Minimal LLM<br/>Extract environments]
        OTE[005 Other Elements Extractor<br/>🐍 Minimal LLM<br/>Extract emotions/effects]
    end

    %% Enhancement Layer (Strategic LLM)
    subgraph ENHANCEMENT["🎨 Enhancement Layer - Strategic LLM Usage"]
        DE[006 Description Enhancer<br/>🤖 LLM Component<br/>Creative visual enhancement]
        SG[007 Shot Generator<br/>🎬 Director of Photography<br/>Professional cinematography]
    end

    %% Optimization Layer (User-Controlled LLM)
    subgraph OPTIMIZATION["🎯 Optimization Layer - User-Defined Limits"]
        SO[010 Storyboard Optimizer<br/>🎯 User-Defined Character Limits<br/>Final prompt optimization]
    end

    %% Language Model
    LLM[🤖 Language Model<br/>Gemini 2.5<br/>Single shared instance]

    %% User Configuration
    subgraph USERCONFIG["⚙️ User Configuration"]
        MAX_LENGTH[📏 Max Prompt Length<br/>Default: 500 characters]
        MIN_LENGTH[📏 Min Prompt Length<br/>Default: 200 characters]
        FOCUS[🎯 Optimization Focus<br/>visual_detail, narrative_flow<br/>technical_precision, artistic_style<br/>balanced]
    end

    %% Data Flow - Foundation
    SP --> CE
    SP --> OE
    SP --> EE
    SP --> OTE
    SP --> SD

    %% Data Flow - Extraction to Enhancement
    CE --> DE
    OE --> DE
    EE --> DE
    OTE --> DE

    %% Data Flow - Enhancement to Assembly
    DE --> SG
    DE --> SD
    SG --> SD

    %% Data Flow - Assembly to Validation
    SD --> CM

    %% Data Flow - Validation to Optimization
    CM --> SO

    %% LLM Connections (Minimal Usage)
    LLM -.->|Only for ambiguous cases| CE
    LLM -.->|Only for ambiguous cases| OE
    LLM -.->|Only for ambiguous cases| EE
    LLM -.->|Only for ambiguous cases| OTE

    %% LLM Connections (Strategic Usage)
    LLM -->|Creative enhancement| DE
    LLM -->|Cinematography| SG

    %% LLM Connections (Final Optimization)
    LLM -->|Final optimization| SO

    %% User Configuration Connections
    MAX_LENGTH --> SO
    MIN_LENGTH --> SO
    FOCUS --> SO

    %% Final Output
    SO --> FINAL[🎬 Final Output<br/>Optimized Storyboard Prompts<br/>Ready for Video Generation]

    %% Key Features Annotations
    FEATURES[✅ Key Features:<br/>- User-defined character limits<br/>- No quality settings always best<br/>- Complete movie processing<br/>- snake_case identifiers<br/>- Professional storyboard output<br/>- Minimal LLM token usage<br/>- Separated extraction architecture]

    %% Styling
    classDef foundation fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef extraction fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef enhancement fill:#e1f5fe,stroke:#2196f3,stroke-width:2px
    classDef optimization fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef llm fill:#ffebee,stroke:#f44336,stroke-width:3px
    classDef userconfig fill:#fff8e1,stroke:#ffc107,stroke-width:2px
    classDef final fill:#e8f5e8,stroke:#4caf50,stroke-width:4px
    classDef features fill:#f5f5f5,stroke:#757575,stroke-width:1px
    classDef title fill:#e3f2fd,stroke:#1976d2,stroke-width:3px

    class SP,SD,CM foundation
    class CE,OE,EE,OTE extraction
    class DE,SG enhancement
    class SO optimization
    class LLM llm
    class MAX_LENGTH,MIN_LENGTH,FOCUS userconfig
    class FINAL final
    class FEATURES features
    class title title
